
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌋 Visualizador de Datos Sísmicos</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: #ffffff;
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 3em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }
        
        .header .subtitle {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.15);
            padding: 20px;
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(5px);
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-card h3 {
            margin-bottom: 10px;
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .stat-value {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 0.9em;
            opacity: 0.8;
        }
        
        .chart-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }
        
        .chart-title {
            color: #2c3e50;
            font-size: 1.8em;
            margin-bottom: 20px;
            text-align: center;
            font-weight: bold;
        }
        
        .chart {
            width: 100%;
            height: 400px;
            position: relative;
        }
        
        .alert-panel {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            border-left: 5px solid #ff6b6b;
        }
        
        .alert-level {
            display: inline-block;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: bold;
            font-size: 1.2em;
            margin-bottom: 10px;
        }
        
        .alert-red {
            background: #ff6b6b;
            color: white;
        }
        
        .alert-orange {
            background: #ffa500;
            color: white;
        }
        
        .alert-yellow {
            background: #ffeb3b;
            color: #333;
        }
        
        .alert-green {
            background: #4caf50;
            color: white;
        }
        
        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .control-btn {
            padding: 10px 20px;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 25px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .control-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        .control-btn.active {
            background: #4caf50;
        }
        
        .tooltip {
            position: absolute;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 0.9em;
            pointer-events: none;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.3);
            opacity: 0.8;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .controls {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌋 Visualizador de Datos Sísmicos</h1>
            <p class="subtitle">Sistema de Predicción Volcánica - Análisis en Tiempo Real</p>
        </div>
        
        <div class="stats-grid">
            <div class="stat-card">
                <h3>📊 Eventos Totales</h3>
                <div class="stat-value" id="total-events">--</div>
                <div class="stat-label">Últimas 24 horas</div>
            </div>
            
            <div class="stat-card">
                <h3>📈 Magnitud Máxima</h3>
                <div class="stat-value" id="max-magnitude">--</div>
                <div class="stat-label">Escala de Richter</div>
            </div>
            
            <div class="stat-card">
                <h3>🎯 Probabilidad de Erupción</h3>
                <div class="stat-value" id="eruption-probability">--</div>
                <div class="stat-label">Análisis FFM</div>
            </div>
            
            <div class="stat-card">
                <h3>⚡ Aceleración Máxima</h3>
                <div class="stat-value" id="max-acceleration">--</div>
                <div class="stat-label">Derivada Segunda</div>
            </div>
        </div>
        
        <div class="alert-panel">
            <div class="alert-level" id="alert-level">VERDE</div>
            <div>
                <strong>Estado del Sistema:</strong> <span id="alert-message">Monitoreo normal</span>
            </div>
        </div>
        
        <div class="controls">
            <button class="control-btn active" onclick="showChart('magnitude', event)">📊 Magnitud vs Tiempo</button>
            <button class="control-btn" onclick="showChart('derivatives', event)">📈 Derivadas</button>
            <button class="control-btn" onclick="showChart('frequency', event)">📋 Distribución de Frecuencia</button>
            <button class="control-btn" onclick="showChart('patterns', event)">🔍 Patrones</button>
            <button class="control-btn" onclick="toggleRealTime(event)">⏱️ Tiempo Real</button>
        </div>
        
        <div class="chart-container">
            <div class="chart-title" id="chart-title">Magnitud Sísmica vs Tiempo</div>
            <div class="chart" id="main-chart"></div>
        </div>
        
        <div class="chart-container">
            <div class="chart-title">Análisis de Aceleración (Segunda Derivada)</div>
            <div class="chart" id="acceleration-chart"></div>
        </div>
        
        <div class="chart-container">
            <div class="chart-title">Patrón de Actividad en Tiempo Real</div>
            <div class="chart" id="realtime-chart"></div>
        </div>
        
        <div class="tooltip" id="tooltip"></div>
        
        <div class="footer">
            <p>Sistema desarrollado para análisis y predicción de actividad volcánica</p>
            <p>Datos actualizados: <span id="last-update">--</span></p>
        </div>
    </div>

    <script src="https://d3js.org/d3.v7.min.js"></script>
    <script>
        // Datos sísmicos (serán reemplazados por datos reales)
        let seismicData = [
  {
    "timestamp": "2025-07-09T03:33:17.426868",
    "magnitude": 2.392938371195723,
    "depth": 9.167091998077586,
    "frequency": 1.7876270072767075,
    "duration": 2.6550888088599294
  },
  {
    "timestamp": "2025-07-09T04:33:17.426868",
    "magnitude": 2.438937939571126,
    "depth": 10.902786601603223,
    "frequency": 2.4039790705600743,
    "duration": 9.817259884653847
  },
  {
    "timestamp": "2025-07-09T06:33:17.426868",
    "magnitude": 1.9618638029687219,
    "depth": 11.477646195992541,
    "frequency": 2.2645288318736774,
    "duration": 3.7601911534332593
  },
  {
    "timestamp": "2025-07-09T08:33:17.426868",
    "magnitude": 1.8771444893592488,
    "depth": 11.593940274516465,
    "frequency": 0.7685505347430576,
    "duration": 4.281420425639098
  },
  {
    "timestamp": "2025-07-09T12:33:17.426868",
    "magnitude": 1.364983460907,
    "depth": 8.91375863225926,
    "frequency": 1.2895329026637163,
    "duration": 5.549738051497465
  },
  {
    "timestamp": "2025-07-09T15:33:17.426868",
    "magnitude": 2.268801917102642,
    "depth": 9.943305638808578,
    "frequency": 4.322443073350053,
    "duration": 7.382325586176035
  },
  {
    "timestamp": "2025-07-09T16:33:17.426868",
    "magnitude": 2.444886765140443,
    "depth": 4.9674220014264225,
    "frequency": 1.953315112339302,
    "duration": 3.936992228411984
  },
  {
    "timestamp": "2025-07-09T18:33:17.426868",
    "magnitude": 2.1997217857061844,
    "depth": 6.65607185621237,
    "frequency": 5.056794443979866,
    "duration": 1.728567568486898
  },
  {
    "timestamp": "2025-07-09T20:33:17.426868",
    "magnitude": 2.8989726057980336,
    "depth": 4.934079517465668,
    "frequency": 4.743330265857402,
    "duration": 6.233674639282787
  },
  {
    "timestamp": "2025-07-09T21:33:17.426868",
    "magnitude": 3.0437257228777983,
    "depth": 6.697331760818581,
    "frequency": 8.394752291764625,
    "duration": 13.527885961338367
  },
  {
    "timestamp": "2025-07-09T22:33:17.426868",
    "magnitude": 3.8856659190527276,
    "depth": 5.59736085765789,
    "frequency": 2.0114146192034665,
    "duration": 5.221115086263958
  },
  {
    "timestamp": "2025-07-10T00:33:17.426868",
    "magnitude": 5.032082052591781,
    "depth": 7.898918499274935,
    "frequency": 4.506375115551094,
    "duration": 15.007993663618427
  },
  {
    "timestamp": "2025-07-10T00:43:17.426868",
    "magnitude": 3.8181979174093326,
    "depth": 6.7843856035478325,
    "frequency": 9.580523360681546,
    "duration": 4.498231313719389
  },
  {
    "timestamp": "2025-07-10T01:43:17.426868",
    "magnitude": 4.110710449438246,
    "depth": 3.1288455231902885,
    "frequency": 8.63095209053051,
    "duration": 10.940151179454944
  },
  {
    "timestamp": "2025-07-10T02:08:17.426868",
    "magnitude": 3.4595777385864555,
    "depth": 4.572956362346078,
    "frequency": 10.538210721099153,
    "duration": 26.388248412060072
  },
  {
    "timestamp": "2025-07-10T03:08:17.426868",
    "magnitude": 4.342598240367953,
    "depth": 5.722823356914774,
    "frequency": 9.20311173587098,
    "duration": 19.122201560772396
  },
  {
    "timestamp": "2025-07-09T23:33:17.427163",
    "magnitude": 4.948198531670901,
    "depth": 2.705664621758118,
    "frequency": 2.1647298366541428,
    "duration": 23.146802401856807
  },
  {
    "timestamp": "2025-07-09T23:43:17.427203",
    "magnitude": 2.679780362025698,
    "depth": 7.197287783925977,
    "frequency": 9.014397404880622,
    "duration": 3.7756629817592264
  },
  {
    "timestamp": "2025-07-09T23:53:17.427218",
    "magnitude": 4.195371402179441,
    "depth": 5.161023156115298,
    "frequency": 11.127829014659367,
    "duration": 1.4677469941554881
  },
  {
    "timestamp": "2025-07-10T00:03:17.427232",
    "magnitude": 3.9487481733800105,
    "depth": 5.868706701396376,
    "frequency": 3.2254350180261184,
    "duration": 5.439044938618419
  },
  {
    "timestamp": "2025-07-10T00:13:17.427241",
    "magnitude": 3.115682492336567,
    "depth": 3.7226540188620123,
    "frequency": 10.687584137445475,
    "duration": 17.07711424181539
  },
  {
    "timestamp": "2025-07-10T00:23:17.427250",
    "magnitude": 5.237963713648951,
    "depth": 1.3051402465932838,
    "frequency": 12.783379956778028,
    "duration": 11.36452943381211
  },
  {
    "timestamp": "2025-07-10T00:33:17.427259",
    "magnitude": 3.066688256938841,
    "depth": 7.967509374238122,
    "frequency": 6.574599546851734,
    "duration": 21.443806083089505
  },
  {
    "timestamp": "2025-07-10T00:43:17.427268",
    "magnitude": 3.2457020300111084,
    "depth": 5.84191259090124,
    "frequency": 11.675669392996074,
    "duration": 18.202130580304413
  },
  {
    "timestamp": "2025-07-10T00:53:17.427277",
    "magnitude": 2.5289460832182806,
    "depth": 3.4041920983382745,
    "frequency": 6.584268098166191,
    "duration": 7.98482103398511
  },
  {
    "timestamp": "2025-07-10T01:03:17.427286",
    "magnitude": 3.7959485396965076,
    "depth": 1.916264654648565,
    "frequency": 10.332743702297003,
    "duration": 4.071346066647601
  }
] || [];
        let derivativeData = [
  {
    "index": 0,
    "first_derivative": -0.10663467622271039,
    "second_derivative": 0.00107030299507678
  },
  {
    "index": 1,
    "first_derivative": -0.10556437322763361,
    "second_derivative": -0.02043736960870764
  },
  {
    "index": 2,
    "first_derivative": -0.1679467850488333,
    "second_derivative": 0.009563612006086512
  },
  {
    "index": 3,
    "first_derivative": -0.06730992520328756,
    "second_derivative": 0.03775104414931122
  },
  {
    "index": 4,
    "first_derivative": 0.05855947984703404,
    "second_derivative": 0.03273577220490244
  },
  {
    "index": 5,
    "first_derivative": 0.16184048023102954,
    "second_derivative": 0.017566138157783375
  },
  {
    "index": 6,
    "first_derivative": 0.12882403247816754,
    "second_derivative": -0.02818788601030388
  },
  {
    "index": 7,
    "first_derivative": 0.07727682220011789,
    "second_derivative": 0.030011226991588834
  },
  {
    "index": 8,
    "first_derivative": 0.24886894044452287,
    "second_derivative": 0.2219120422476498
  },
  {
    "index": 9,
    "first_derivative": 0.7430129489430672,
    "second_derivative": 0.08608123830368676
  },
  {
    "index": 10,
    "first_derivative": 0.4210314170518964,
    "second_derivative": -0.5361671294719684
  },
  {
    "index": 11,
    "first_derivative": -0.32932135393678774,
    "second_derivative": -1.42732335896491
  },
  {
    "index": 12,
    "first_derivative": -1.2441793012275333,
    "second_derivative": 0.8162621240212544
  },
  {
    "index": 13,
    "first_derivative": -0.05723396679236632,
    "second_derivative": 6.008964661655276
  },
  {
    "index": 14,
    "first_derivative": 0.7588089677297815,
    "second_derivative": 9.085331144831052
  },
  {
    "index": 15,
    "first_derivative": 2.971209806196499,
    "second_derivative": 5.1485220120497885
  },
  {
    "index": 16,
    "first_derivative": 2.4749829974889836,
    "second_derivative": -25.82653264572917
  },
  {
    "index": 17,
    "first_derivative": -5.6376317331307915,
    "second_derivative": -62.600928259580805
  },
  {
    "index": 18,
    "first_derivative": -7.95850520227678,
    "second_derivative": 11.062156929654707
  },
  {
    "index": 19,
    "first_derivative": -3.7939389115216606,
    "second_derivative": 33.07353371341339
  },
  {
    "index": 20,
    "first_derivative": -2.446249500690727,
    "second_derivative": 29.622339413574267
  },
  {
    "index": 21,
    "first_derivative": 1.1431210228342235,
    "second_derivative": 10.694832841777018
  },
  {
    "index": 22,
    "first_derivative": 1.118694833375768,
    "second_derivative": -0.7569375267708354
  },
  {
    "index": 23,
    "first_derivative": 0.512339836521706,
    "second_derivative": -0.9265645999149811
  },
  {
    "index": 24,
    "first_derivative": 0.11491662438565048,
    "second_derivative": -0.39161477560335417
  },
  {
    "index": 25,
    "first_derivative": -0.042447762249711474,
    "second_derivative": -0.15736438663536195
  }
] || [];
        let ffmData = {
  "failure_probability": 0.8217567264771249,
  "time_to_failure": 0.5,
  "confidence_level": 0.2081419110030427,
  "acceleration_trend": "ACELERACI\u00d3N EXPONENCIAL",
  "precursor_indicators": [
    "ACELERACI\u00d3N S\u00cdSMICA CR\u00cdTICA",
    "ALTA VARIABILIDAD",
    "PATR\u00d3N CR\u00cdTICO DETECTADO"
  ]
} || {};
        let alertData = {
  "level": "RED",
  "message": "Indicadores cr\u00edticos sugieren erupci\u00f3n inmediata. Probabilidad de erupci\u00f3n: 82.2% | Tiempo estimado: 0.5 horas | Precursores detectados: 3 | Tendencia: ACELERACI\u00d3N EXPONENCIAL",
  "probability": 0.8217567264771249,
  "timestamp": "2025-07-10T03:33:17.428638"
} || {};
        
        // Variables globales
        let currentChart = 'magnitude';
        let isRealTimeActive = false;
        let realTimeInterval = null;
        
        // Funciones principales
        function updateStats() {
            if (!seismicData || seismicData.length === 0) {
                document.getElementById('total-events').textContent = '0';
                document.getElementById('max-magnitude').textContent = '0.0';
                document.getElementById('eruption-probability').textContent = '0.0%';
                document.getElementById('max-acceleration').textContent = '0.0';
                return;
            }
            
            const totalEvents = seismicData.length;
            const maxMagnitude = Math.max(...seismicData.map(d => d.magnitude || 0));
            const maxAcceleration = derivativeData && derivativeData.length > 0 ? 
                Math.max(...derivativeData.map(d => Math.abs(d.second_derivative || 0))) : 0;
            const eruptionProbability = ffmData.failure_probability || 0;
            
            document.getElementById('total-events').textContent = totalEvents;
            document.getElementById('max-magnitude').textContent = maxMagnitude.toFixed(2);
            document.getElementById('eruption-probability').textContent = (eruptionProbability * 100).toFixed(1) + '%';
            document.getElementById('max-acceleration').textContent = maxAcceleration.toFixed(2);
        }
        
        function updateAlertStatus() {
            const alertLevel = alertData.level || 'GREEN';
            const alertMessage = alertData.message || 'Sistema funcionando normalmente';
            
            const levelElement = document.getElementById('alert-level');
            levelElement.textContent = alertLevel;
            levelElement.className = `alert-level alert-${alertLevel.toLowerCase()}`;
            
            document.getElementById('alert-message').textContent = alertMessage;
        }
        
        function updateLastUpdate() {
            document.getElementById('last-update').textContent = new Date().toLocaleString();
        }
        
        // Inicializar visualización
        document.addEventListener('DOMContentLoaded', function() {
            updateStats();
            updateAlertStatus();
            showChart('magnitude');
            updateLastUpdate();
        });
        
        function showChart(chartType, event) {
            // Actualizar botones
            document.querySelectorAll('.control-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // Solo agregar clase active si hay un evento (llamada desde botón)
            if (event && event.target) {
                event.target.classList.add('active');
            } else {
                // Si no hay evento, encontrar el botón correspondiente
                const buttons = document.querySelectorAll('.control-btn');
                buttons.forEach(btn => {
                    if (btn.textContent.includes('Magnitud') && chartType === 'magnitude') {
                        btn.classList.add('active');
                    }
                });
            }
            
            currentChart = chartType;
            
            try {
                switch(chartType) {
                    case 'magnitude':
                        drawMagnitudeChart();
                        break;
                    case 'derivatives':
                        drawDerivativeChart();
                        break;
                    case 'frequency':
                        drawFrequencyChart();
                        break;
                    case 'patterns':
                        drawPatternChart();
                        break;
                    default:
                        console.warn('Tipo de gráfico no reconocido:', chartType);
                        drawMagnitudeChart();
                }
            } catch (error) {
                console.error('Error al dibujar gráfico:', error);
                document.getElementById('chart-title').textContent = 'Error al cargar gráfico';
            }
        }
        
        function drawMagnitudeChart() {
            const container = d3.select('#main-chart');
            container.selectAll('*').remove();
            
            // Validar datos
            if (!seismicData || seismicData.length === 0) {
                container.append('div')
                    .style('text-align', 'center')
                    .style('padding', '50px')
                    .style('color', '#666')
                    .html('📊 No hay datos sísmicos disponibles');
                document.getElementById('chart-title').textContent = 'Datos No Disponibles';
                return;
            }
            
            const margin = {top: 20, right: 30, bottom: 40, left: 50};
            const width = container.node().getBoundingClientRect().width - margin.left - margin.right;
            const height = 400 - margin.top - margin.bottom;
            
            if (width <= 0 || height <= 0) {
                console.warn('Dimensiones inválidas para el gráfico');
                return;
            }
            
            const svg = container.append('svg')
                .attr('width', width + margin.left + margin.right)
                .attr('height', height + margin.top + margin.bottom);
            
            const g = svg.append('g')
                .attr('transform', `translate(${margin.left},${margin.top})`);
            
            // Escalas
            const xScale = d3.scaleTime()
                .domain(d3.extent(seismicData, d => new Date(d.timestamp)))
                .range([0, width]);
            
            const yScale = d3.scaleLinear()
                .domain(d3.extent(seismicData, d => d.magnitude))
                .range([height, 0]);
            
            // Línea
            const line = d3.line()
                .x(d => xScale(new Date(d.timestamp)))
                .y(d => yScale(d.magnitude))
                .curve(d3.curveMonotoneX);
            
            // Dibujar ejes
            g.append('g')
                .attr('transform', `translate(0,${height})`)
                .call(d3.axisBottom(xScale).tickFormat(d3.timeFormat('%H:%M')));
            
            g.append('g')
                .call(d3.axisLeft(yScale));
            
            // Dibujar línea
            g.append('path')
                .datum(seismicData)
                .attr('fill', 'none')
                .attr('stroke', '#ff6b6b')
                .attr('stroke-width', 2)
                .attr('d', line);
            
            // Puntos
            g.selectAll('.dot')
                .data(seismicData)
                .enter().append('circle')
                .attr('class', 'dot')
                .attr('cx', d => xScale(new Date(d.timestamp)))
                .attr('cy', d => yScale(d.magnitude))
                .attr('r', 4)
                .attr('fill', d => d.magnitude > 4 ? '#ff4444' : '#4CAF50')
                .on('mouseover', function(event, d) {
                    showTooltip(event, `Magnitud: ${d.magnitude.toFixed(2)}<br>Tiempo: ${new Date(d.timestamp).toLocaleString()}`);
                })
                .on('mouseout', hideTooltip);
            
            // Etiquetas de ejes
            g.append('text')
                .attr('transform', 'rotate(-90)')
                .attr('y', 0 - margin.left)
                .attr('x', 0 - (height / 2))
                .attr('dy', '1em')
                .style('text-anchor', 'middle')
                .style('fill', '#2c3e50')
                .text('Magnitud (Richter)');
            
            g.append('text')
                .attr('transform', `translate(${width / 2}, ${height + margin.bottom})`)
                .style('text-anchor', 'middle')
                .style('fill', '#2c3e50')
                .text('Tiempo');
            
            document.getElementById('chart-title').textContent = 'Magnitud Sísmica vs Tiempo';
        }
        
        function drawDerivativeChart() {
            const container = d3.select('#main-chart');
            container.selectAll('*').remove();
            
            // Validar datos
            if (!derivativeData || derivativeData.length === 0) {
                container.append('div')
                    .style('text-align', 'center')
                    .style('padding', '50px')
                    .style('color', '#666')
                    .html('📈 No hay datos de derivadas disponibles');
                document.getElementById('chart-title').textContent = 'Datos de Derivadas No Disponibles';
                return;
            }
            
            const margin = {top: 20, right: 30, bottom: 40, left: 50};
            const width = container.node().getBoundingClientRect().width - margin.left - margin.right;
            const height = 400 - margin.top - margin.bottom;
            
            if (width <= 0 || height <= 0) {
                console.warn('Dimensiones inválidas para el gráfico de derivadas');
                return;
            }
            
            const svg = container.append('svg')
                .attr('width', width + margin.left + margin.right)
                .attr('height', height + margin.top + margin.bottom);
            
            const g = svg.append('g')
                .attr('transform', `translate(${margin.left},${margin.top})`);
            
            // Escalas
            const xScale = d3.scaleLinear()
                .domain([0, derivativeData.length - 1])
                .range([0, width]);
            
            const yScale = d3.scaleLinear()
                .domain(d3.extent(derivativeData, d => d.first_derivative || 0))
                .range([height, 0]);
            
            // Línea para primera derivada
            const line = d3.line()
                .x((d, i) => xScale(i))
                .y(d => yScale(d.first_derivative || 0))
                .curve(d3.curveMonotoneX);
            
            // Dibujar ejes
            g.append('g')
                .attr('transform', `translate(0,${height})`)
                .call(d3.axisBottom(xScale));
            
            g.append('g')
                .call(d3.axisLeft(yScale));
            
            // Línea de referencia en y=0
            g.append('line')
                .attr('x1', 0)
                .attr('x2', width)
                .attr('y1', yScale(0))
                .attr('y2', yScale(0))
                .attr('stroke', '#666')
                .attr('stroke-dasharray', '3,3');
            
            // Dibujar línea
            g.append('path')
                .datum(derivativeData)
                .attr('fill', 'none')
                .attr('stroke', '#2196F3')
                .attr('stroke-width', 2)
                .attr('d', line);
            
            // Puntos críticos
            g.selectAll('.critical-point')
                .data(derivativeData.filter(d => Math.abs(d.first_derivative || 0) > 0.5))
                .enter().append('circle')
                .attr('class', 'critical-point')
                .attr('cx', (d, i) => xScale(derivativeData.indexOf(d)))
                .attr('cy', d => yScale(d.first_derivative || 0))
                .attr('r', 6)
                .attr('fill', '#ff6b6b')
                .attr('stroke', '#fff')
                .attr('stroke-width', 2);
            
            document.getElementById('chart-title').textContent = 'Análisis de Derivadas - Velocidad de Cambio';
        }
        
        function drawFrequencyChart() {
            const container = d3.select('#main-chart');
            container.selectAll('*').remove();
            
            // Validar datos
            if (!seismicData || seismicData.length === 0) {
                container.append('div')
                    .style('text-align', 'center')
                    .style('padding', '50px')
                    .style('color', '#666')
                    .html('📋 No hay datos para histograma');
                document.getElementById('chart-title').textContent = 'Datos No Disponibles';
                return;
            }
            
            // Crear histograma de magnitudes
            const magnitudes = seismicData.map(d => d.magnitude || 0);
            const bins = d3.histogram()
                .domain(d3.extent(magnitudes))
                .thresholds(10)(magnitudes);
            
            const margin = {top: 20, right: 30, bottom: 40, left: 50};
            const width = container.node().getBoundingClientRect().width - margin.left - margin.right;
            const height = 400 - margin.top - margin.bottom;
            
            const svg = container.append('svg')
                .attr('width', width + margin.left + margin.right)
                .attr('height', height + margin.top + margin.bottom);
            
            const g = svg.append('g')
                .attr('transform', `translate(${margin.left},${margin.top})`);
            
            const xScale = d3.scaleLinear()
                .domain(d3.extent(magnitudes))
                .range([0, width]);
            
            const yScale = d3.scaleLinear()
                .domain([0, d3.max(bins, d => d.length)])
                .range([height, 0]);
            
            // Dibujar ejes
            g.append('g')
                .attr('transform', `translate(0,${height})`)
                .call(d3.axisBottom(xScale));
            
            g.append('g')
                .call(d3.axisLeft(yScale));
            
            // Barras
            g.selectAll('.bar')
                .data(bins)
                .enter().append('rect')
                .attr('class', 'bar')
                .attr('x', d => xScale(d.x0))
                .attr('y', d => yScale(d.length))
                .attr('width', d => xScale(d.x1) - xScale(d.x0) - 1)
                .attr('height', d => height - yScale(d.length))
                .attr('fill', '#4CAF50')
                .attr('opacity', 0.7);
            
            document.getElementById('chart-title').textContent = 'Distribución de Frecuencia de Magnitudes';
        }
        
        function drawPatternChart() {
            // Implementar gráfico de patrones
            document.getElementById('chart-title').textContent = 'Análisis de Patrones Sísmicos';
        }
        
        function toggleRealTime(event) {
            isRealTimeActive = !isRealTimeActive;
            const btn = event ? event.target : document.querySelector('.control-btn:last-child');
            
            if (isRealTimeActive) {
                btn.textContent = '⏹️ Detener Tiempo Real';
                btn.classList.add('active');
                startRealTimeSimulation();
            } else {
                btn.textContent = '⏱️ Tiempo Real';
                btn.classList.remove('active');
                stopRealTimeSimulation();
            }
        }
        
        function startRealTimeSimulation() {
            realTimeInterval = setInterval(() => {
                // Simular nuevos datos
                const newMagnitude = Math.random() * 3 + 1;
                const newTimestamp = new Date().toISOString();
                
                seismicData.push({
                    timestamp: newTimestamp,
                    magnitude: newMagnitude,
                    depth: Math.random() * 10 + 5
                });
                
                // Mantener solo los últimos 100 puntos
                if (seismicData.length > 100) {
                    seismicData.shift();
                }
                
                updateStats();
                if (currentChart === 'magnitude') {
                    drawMagnitudeChart();
                }
                
                updateLastUpdate();
            }, 2000);
        }
        
        function stopRealTimeSimulation() {
            if (realTimeInterval) {
                clearInterval(realTimeInterval);
                realTimeInterval = null;
            }
        }
        
        function showTooltip(event, html) {
            const tooltip = document.getElementById('tooltip');
            tooltip.innerHTML = html;
            tooltip.style.left = (event.pageX + 10) + 'px';
            tooltip.style.top = (event.pageY - 10) + 'px';
            tooltip.style.opacity = '1';
        }
        
        function hideTooltip() {
            document.getElementById('tooltip').style.opacity = '0';
        }
        
        function updateLastUpdate() {
            document.getElementById('last-update').textContent = new Date().toLocaleString();
        }
        
        // Redimensionar gráficos cuando cambie el tamaño de la ventana
        window.addEventListener('resize', function() {
            if (currentChart) {
                showChart(currentChart);
            }
        });
    </script>
</body>
</html>
        