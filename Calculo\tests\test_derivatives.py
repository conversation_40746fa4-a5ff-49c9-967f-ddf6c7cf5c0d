"""
Tests for derivative calculation module
"""

import pytest
import numpy as np
from datetime import datetime, timedelta
from core.derivatives import DerivativeCalculator
from models.volcanic_data import SeismicReading

class TestDerivativeCalculator:
    
    def setup_method(self):
        self.calc = DerivativeCalculator()
    
    def test_calculate_derivatives_simple(self):
        """Test basic derivative calculation"""
        # Create simple increasing data
        start_time = datetime.utcnow()
        seismic_data = [
            SeismicReading(
                timestamp=start_time + timedelta(hours=i),
                magnitude=float(i),
                frequency=1.0,
                duration=1.0,
                depth=5.0
            )
            for i in range(5)
        ]
        
        result = self.calc.calculate_derivatives(seismic_data)
        
        # Check that derivatives are calculated
        assert len(result.first_derivative) == 5
        assert len(result.second_derivative) == 5
        assert len(result.acceleration_points) == 5
        assert result.max_acceleration >= 0
        assert result.acceleration_trend is not None
    
    def test_calculate_derivatives_with_steps(self):
        """Test derivative calculation with detailed steps"""
        start_time = datetime.utcnow()
        seismic_data = [
            SeismicReading(
                timestamp=start_time + timedelta(hours=i),
                magnitude=float(i**2),  # Quadratic increase
                frequency=1.0,
                duration=1.0,
                depth=5.0
            )
            for i in range(4)
        ]
        
        result = self.calc.calculate_derivatives(seismic_data, show_steps=True)
        
        # Check that calculation steps are included
        assert result.calculation_steps is not None
        assert len(result.calculation_steps) > 0
        
        # Check step structure
        step = result.calculation_steps[0]
        assert 'step_number' in step
        assert 'description' in step
        assert 'formula' in step
        assert 'calculation' in step
        assert 'result' in step
    
    def test_acceleration_trend_analysis(self):
        """Test acceleration trend analysis"""
        start_time = datetime.utcnow()
        
        # Create accelerating data
        seismic_data = [
            SeismicReading(
                timestamp=start_time + timedelta(hours=i),
                magnitude=float(np.exp(i * 0.5)),  # Exponential increase
                frequency=1.0,
                duration=1.0,
                depth=5.0
            )
            for i in range(6)
        ]
        
        result = self.calc.calculate_derivatives(seismic_data)
        
        # Should detect acceleration
        assert "ACELERACIÓN" in result.acceleration_trend or "ESTABLE" in result.acceleration_trend
    
    def test_insufficient_data(self):
        """Test handling of insufficient data"""
        start_time = datetime.utcnow()
        seismic_data = [
            SeismicReading(
                timestamp=start_time,
                magnitude=1.0,
                frequency=1.0,
                duration=1.0,
                depth=5.0
            )
        ]
        
        with pytest.raises(ValueError, match="Se necesitan al menos 3 puntos"):
            self.calc.calculate_derivatives(seismic_data)
    
    def test_calculation_history(self):
        """Test calculation history tracking"""
        start_time = datetime.utcnow()
        seismic_data = [
            SeismicReading(
                timestamp=start_time + timedelta(hours=i),
                magnitude=float(i),
                frequency=1.0,
                duration=1.0,
                depth=5.0
            )
            for i in range(5)
        ]
        
        # Clear history first
        self.calc.clear_history()
        
        # Perform calculation
        self.calc.calculate_derivatives(seismic_data)
        
        # Check history
        history = self.calc.get_calculation_history()
        assert len(history) == 1
        assert 'timestamp' in history[0]
        assert 'data_points' in history[0]
        assert 'max_acceleration' in history[0]
        assert 'trend' in history[0]