# 🌋 Índice de Aceleración de Precursores - Volcano App

## 📋 Resumen

El **Índice de Aceleración de Precursores** es una funcionalidad avanzada implementada en volcanoApp que analiza la tasa de cambio (primera derivada) y la aceleración de esa tasa (segunda derivada) de datos de precursores volcánicos para generar alertas dinámicas, sin depender de umbrales estáticos.

## 🎯 Características Principales

### ✅ Análisis Matemático Avanzado
- **Primera Derivada**: Calcula la tasa de cambio de los datos de precursores
- **Segunda Derivada**: Calcula la aceleración de la tasa de cambio
- **Algoritmos Robustos**: Manejo de valores nulos y casos edge

### ✅ Sistema de Alertas Dinámico
- **Verde (≤ 1)**: Actividad estable
- **Amarillo (1-5)**: Precaución, actividad acelerando
- **Rojo (> 5)**: Alerta, aceleración peligrosa detectada

### ✅ Visualización Interactiva
- **Tres Gráficos**: Datos originales, primera derivada, segunda derivada
- **Recharts**: Gráficos responsivos y accesibles
- **Estadísticas**: Métricas detalladas de cada análisis

### ✅ Integración con Supabase
- **Alertas Automáticas**: Envío automático a sistema de alertas
- **Notificaciones Push**: Integración con sistema de notificaciones
- **Prevención de Spam**: Control inteligente de frecuencia de alertas

## 📁 Estructura de Archivos

```
volcanoApp/
├── types/precursor.ts                    # Tipos TypeScript
├── utils/precursorAnalysis.ts           # Lógica matemática
├── services/precursorAlerts.ts          # Integración con alertas
├── components/PrecursorAcceleration.tsx # Componente principal
├── app/(tabs)/precursors.tsx           # Pantalla dedicada
└── __tests__/
    ├── precursorAnalysis.test.ts       # Tests unitarios
    └── precursorAnalysis.manual.test.js # Tests manuales
```

## 🔧 API Principal

### `analizarPrecursor(datos: number[])`
```typescript
const resultado = analizarPrecursor([2, 3, 3, 4, 6, 9, 14, 22, 35, 50, 68]);
// Retorna: { datosOriginales, primeraderivada, segundaDerivada }
```

### `determinarNivelAlerta(segundaDerivada: (number | null)[])`
```typescript
const alerta = determinarNivelAlerta(segundaDerivada);
// Retorna: { nivel: 'Verde'|'Amarillo'|'Rojo', mensaje, valor, timestamp }
```

### `analisisCompleto(datos: number[])`
```typescript
const { analisis, alerta } = analisisCompleto(datos);
// Retorna análisis completo + determinación de alerta
```

## 🎨 Componente de Visualización

### Props del Componente
```typescript
interface PrecursorAccelerationProps {
  datos: DatosPrecursor;           // Datos a analizar
  umbrales?: UmbralesAlerta;       // Umbrales personalizados
  onAlerta?: (resultado: ResultadoAlerta) => void; // Callback de alerta
  titulo?: string;                 // Título del gráfico
  altura?: number;                 // Altura de gráficos
  mostrarControles?: boolean;      // Mostrar controles
}
```

### Uso del Componente
```typescript
import PrecursorAcceleration from '@/components/PrecursorAcceleration';

<PrecursorAcceleration
  datos={DATOS_PRUEBA}
  onAlerta={(resultado) => console.log('Alerta:', resultado)}
  titulo="Análisis de Actividad Sísmica"
  altura={300}
  mostrarControles={true}
/>
```

## 🚨 Sistema de Alertas

### Configuración de Alertas
```typescript
const CONFIGURACION_ALERTAS = {
  Verde: {
    prioridad: 'low',
    enviarNotificacion: false,
    duracionHoras: 1,
  },
  Amarillo: {
    prioridad: 'medium',
    enviarNotificacion: true,
    duracionHoras: 6,
  },
  Rojo: {
    prioridad: 'high',
    enviarNotificacion: true,
    duracionHoras: 24,
  },
};
```

### Uso del Servicio de Alertas
```typescript
import { precursorAlertsService } from '@/services/precursorAlerts';

// Procesar resultado automáticamente
const resultado = await precursorAlertsService.procesarResultadoAnalisis(
  alerta,
  'Actividad Sísmica',
  'Volcán Villarrica'
);

// Crear alerta manual
const alertaManual = await precursorAlertsService.crearAlertaPrecursor({
  titulo: 'Alerta Manual',
  mensaje: 'Actividad detectada manualmente',
  nivel: 'Amarillo',
  valor: 3.5
});
```

## 📊 Datos de Prueba

### Dataset Principal
```typescript
const DATOS_PRUEBA = {
  valores: [2, 3, 3, 4, 6, 9, 14, 22, 35, 50, 68],
  tipo: 'Actividad Sísmica',
  unidad: 'Eventos/hora',
  volcan: 'Volcán de Prueba',
};
```

### Resultados Esperados
- **Primera Derivada**: `[null, 1, 0, 1, 2, 3, 5, 8, 13, 15, 18]`
- **Segunda Derivada**: `[null, null, -1, 1, 1, 1, 2, 3, 5, 2, 3]`
- **Nivel de Alerta**: `Amarillo` (último valor = 3)

## 🧪 Testing

### Tests Unitarios
```bash
npm test -- __tests__/precursorAnalysis.test.ts
```

### Tests Manuales
```bash
node __tests__/precursorAnalysis.manual.test.js
```

### Casos de Prueba
- ✅ Validación de datos
- ✅ Cálculo de derivadas
- ✅ Determinación de alertas
- ✅ Casos edge (datos insuficientes, valores negativos, constantes)
- ✅ Estadísticas básicas

## 🚀 Navegación

### Acceso a la Funcionalidad
1. **Tab "Precursores"**: Pantalla dedicada con ejemplos
2. **Botón "Precursores"**: Desde pantalla principal
3. **Componente Embebido**: Integrable en otras pantallas

### Pantalla de Precursores
- Selector de ejemplos de datos
- Información del dataset actual
- Análisis interactivo completo
- Guía de interpretación

## ⚡ Optimizaciones de Rendimiento

### React Optimizations
- **React.memo**: Componente principal memoizado
- **useCallback**: Funciones de renderizado optimizadas
- **useMemo**: Cálculos pesados memoizados
- **Lazy Loading**: Carga diferida de gráficos

### Algoritmos Eficientes
- **O(n)**: Complejidad lineal para cálculos
- **Validación Temprana**: Detección rápida de errores
- **Manejo de Memoria**: Limpieza automática de datos

## 🔧 Configuración Avanzada

### Umbrales Personalizados
```typescript
const umbralesCustom: UmbralesAlerta = {
  verde: 0.5,    // Más sensible
  amarillo: 3,   // Umbral medio más bajo
  rojo: 3,       // Alerta roja más temprana
};
```

### Configuración de Gráficos
```typescript
const configuracion: ConfiguracionGrafico = {
  mostrarTendencia: true,
  mostrarPuntos: false,
  suavizar: true,
  colores: {
    datosOriginales: '#3b82f6',
    primeraderivada: '#10b981',
    segundaDerivada: '#f59e0b',
  },
};
```

## 📈 Casos de Uso

### 1. Monitoreo Sísmico
- Análisis de frecuencia de eventos sísmicos
- Detección de enjambres sísmicos
- Alertas de escalada sísmica

### 2. Deformación del Suelo
- Análisis de datos GPS/InSAR
- Detección de inflación volcánica
- Monitoreo de cambios topográficos

### 3. Emisiones Gaseosas
- Análisis de SO2, CO2, H2S
- Detección de cambios en composición
- Alertas de incremento de emisiones

### 4. Temperatura
- Monitoreo de fumarolas
- Análisis de anomalías térmicas
- Detección de calentamiento

## 🔮 Futuras Mejoras

### Análisis Avanzado
- [ ] Análisis de Fourier para frecuencias
- [ ] Machine Learning para patrones
- [ ] Correlación entre múltiples precursores
- [ ] Predicción de tendencias

### Visualización
- [ ] Gráficos 3D interactivos
- [ ] Animaciones temporales
- [ ] Mapas de calor
- [ ] Realidad aumentada

### Integración
- [ ] APIs de datos oficiales
- [ ] Sensores IoT en tiempo real
- [ ] Sistemas de alerta externa
- [ ] Exportación de reportes

---

## 📞 Soporte

Para preguntas o problemas con la funcionalidad de Índice de Aceleración de Precursores, consulta la documentación técnica o contacta al equipo de desarrollo.

**Versión**: 1.0.0  
**Última Actualización**: Junio 2025  
**Compatibilidad**: React Native, Expo, Web
