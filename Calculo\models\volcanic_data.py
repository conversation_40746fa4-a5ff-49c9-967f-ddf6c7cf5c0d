"""
Modelos de datos para el sistema de predicción volcánica
"""

from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
from datetime import datetime
from enum import Enum

class AlertLevel(str, Enum):
    """Niveles de alerta volcánica"""
    GREEN = "GREEN"      # Normal
    YELLOW = "YELLOW"    # Precaución
    ORANGE = "ORANGE"    # Alerta
    RED = "RED"          # Erupción inminente

class SeismicReading(BaseModel):
    """Lectura sísmica individual"""
    timestamp: datetime
    magnitude: float = Field(description="Magnitud sísmica (Richter)")
    frequency: float = Field(description="Frecuencia dominante (Hz)")
    duration: float = Field(description="Duración del evento (segundos)")
    depth: float = Field(description="Profundidad del evento (km)")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class DerivativeResult(BaseModel):
    """Resultado del cálculo de derivadas"""
    first_derivative: List[float] = Field(description="Primera derivada")
    second_derivative: List[float] = Field(description="Segunda derivada")
    acceleration_points: List[float] = Field(description="Puntos de aceleración")
    max_acceleration: float = Field(description="Máxima aceleración detectada")
    acceleration_trend: str = Field(description="Tendencia de aceleración")
    calculation_steps: Optional[List[Dict[str, Any]]] = Field(description="Pasos de cálculo detallados")

class FFMResult(BaseModel):
    """Resultado del análisis FFM (Failure Forecast Model)"""
    failure_probability: float = Field(description="Probabilidad de falla/erupción")
    time_to_failure: Optional[float] = Field(description="Tiempo estimado hasta falla (horas)")
    acceleration_trend: str = Field(description="Tendencia de aceleración")
    confidence_level: float = Field(description="Nivel de confianza del modelo")
    critical_threshold: float = Field(description="Umbral crítico alcanzado")
    precursor_indicators: List[str] = Field(description="Indicadores precursores detectados")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class PredictionResult(BaseModel):
    """Resultado completo de predicción"""
    timestamp: datetime
    eruption_probability: float = Field(description="Probabilidad de erupción")
    alert_level: AlertLevel = Field(description="Nivel de alerta")
    derivative_analysis: DerivativeResult = Field(description="Análisis de derivadas")
    ffm_analysis: FFMResult = Field(description="Análisis FFM")
    calculation_steps: Optional[List[Dict[str, Any]]] = Field(description="Pasos de cálculo")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class CalculationStep(BaseModel):
    """Paso individual de cálculo"""
    step_number: int
    description: str
    formula: str
    input_values: Dict[str, Any]
    calculation: str
    result: float
    explanation: str

class AlertMessage(BaseModel):
    """Mensaje de alerta"""
    id: str
    level: AlertLevel
    message: str
    timestamp: datetime
    probability: float
    recommendations: List[str]
    active: bool = True
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class SystemStatus(BaseModel):
    """Estado del sistema"""
    status: str
    last_update: datetime
    components_status: Dict[str, str]
    active_calculations: int
    total_predictions: int
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }