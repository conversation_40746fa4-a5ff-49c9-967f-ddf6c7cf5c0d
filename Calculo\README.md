# Sistema de Predicción Volcánica - Monolito de Cálculo

## Descripción

Este es un monolito independiente en Python que implementa un sistema de predicción volcánica basado en:
- **Cálculo de derivadas** (primera y segunda) para detectar aceleración sísmica
- **Modelo de Pronóstico de Falla (FFM)** para predecir erupciones
- **Sistema de alertas tempranas** con múltiples niveles
- **Generación de datos sintéticos** para testing y demostración

## Características Principales

### 🧮 Cálculo de Derivadas Paso a Paso
- Primera derivada: velocidad de cambio de la actividad sísmica
- Segunda derivada: aceleración/desaceleración de la actividad
- Visualización detallada de cada paso del cálculo
- Análisis de tendencias de aceleración

### 🔮 Modelo FFM (Failure Forecast Model)
- Predicción de probabilidad de erupción
- Estimación de tiempo hasta falla
- Análisis de clustering temporal
- Identificación de precursores volcánicos

### 🚨 Sistema de Alertas Tempranas
- **GREEN**: Actividad normal
- **YELLOW**: Precaución - actividad incrementada
- **ORANGE**: Alerta - actividad crítica
- **RED**: Emergencia - erupción inminente

### 📊 Generación de Datos Sintéticos
- Escenarios de actividad normal
- Escenarios pre-erupción con aceleración
- Patrones específicos: lineal, exponencial, periódico
- Datos realistas para testing

## Instalación

### Opción 1: Usando UV (Recomendado) 🚀

```powershell
# Configuración automática (instala uv si es necesario)
.\setup-dev.ps1

# O configuración manual
uv sync                    # Solo dependencias básicas
uv sync --extra scientific # Incluir numpy, scipy, matplotlib
uv sync --extra dev        # Incluir herramientas de desarrollo
```

### Opción 2: Usando pip (Tradicional)

```bash
cd /Calculo
pip install -r requirements.txt
```

## Uso

### Con UV (Recomendado)
```powershell
# Ejecutar el servidor
uv run python main.py

# Ejecutar ejemplo
uv run python run_example.py

# Ejecutar tests
uv run pytest
```

### Con pip (Tradicional)
```bash
python main.py
```

El servidor estará disponible en `http://localhost:8000`

### Documentación API
Una vez ejecutado el servidor, visita:
- API docs: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`

## Endpoints Principales

### 1. Predicción Completa
```http
POST /predict
Content-Type: application/json

{
  "seismic_data": [
    {
      "timestamp": "2024-01-01T00:00:00Z",
      "magnitude": 2.3,
      "frequency": 3.5,
      "duration": 5.2,
      "depth": 8.1
    }
  ],
  "time_window": 24,
  "calculation_detail": true
}
```

### 2. Cálculo Solo de Derivadas
```http
POST /calculate/derivatives
Content-Type: application/json

{
  "data_points": [1.0, 1.5, 2.2, 3.1, 4.5, 6.2],
  "time_points": [0, 1, 2, 3, 4, 5],
  "show_steps": true
}
```

### 3. Generar Datos de Prueba
```http
GET /data/generate?days=7&eruption_scenario=true
```

### 4. Análisis FFM
```http
POST /analyze/ffm
Content-Type: application/json

{
  "seismic_data": [...],
  "time_window": 24
}
```

## Estructura del Proyecto

```
Calculo/
├── main.py                 # Servidor FastAPI principal
├── requirements.txt        # Dependencias Python
├── core/                   # Módulos centrales
│   ├── derivatives.py      # Cálculo de derivadas
│   ├── ffm_model.py       # Modelo FFM
│   ├── data_generator.py  # Generador de datos
│   └── alert_system.py    # Sistema de alertas
├── models/                 # Modelos de datos
│   └── volcanic_data.py   # Estructuras de datos
└── tests/                  # Tests unitarios
    └── test_derivatives.py
```

## Librerías Utilizadas

### Core
- **FastAPI**: Framework web moderno y rápido
- **Pydantic**: Validación y serialización de datos
- **Uvicorn**: Servidor ASGI de alto rendimiento

### Cálculo Científico
- **NumPy**: Computación numérica
- **SciPy**: Algoritmos científicos avanzados
- **SymPy**: Matemáticas simbólicas (opcional)

### Visualización y Análisis
- **Matplotlib**: Generación de gráficos
- **Pandas**: Manipulación de datos

### Testing
- **Pytest**: Framework de testing
- **Pytest-asyncio**: Testing asíncrono

## Ejemplos de Uso

### Ejemplo 1: Predicción con Datos Sintéticos
```python
import requests

# Generar datos de prueba
response = requests.get('http://localhost:8000/data/generate?days=3&eruption_scenario=true')
data = response.json()

# Usar datos para predicción
prediction_request = {
    "seismic_data": data["data"],
    "calculation_detail": True
}

prediction = requests.post('http://localhost:8000/predict', json=prediction_request)
result = prediction.json()

print(f"Probabilidad de erupción: {result['eruption_probability']:.2%}")
print(f"Nivel de alerta: {result['alert_level']}")
```

### Ejemplo 2: Cálculo de Derivadas Paso a Paso
```python
# Datos de magnitud sísmica
derivative_request = {
    "data_points": [1.2, 1.8, 2.5, 3.8, 5.2, 7.1, 9.5],
    "time_points": [0, 1, 2, 3, 4, 5, 6],
    "show_steps": True
}

response = requests.post('http://localhost:8000/calculate/derivatives', json=derivative_request)
result = response.json()

# Ver pasos de cálculo
for step in result["calculation_steps"]:
    print(f"Paso {step['step_number']}: {step['description']}")
    print(f"Fórmula: {step['formula']}")
    print(f"Cálculo: {step['calculation']}")
    print(f"Resultado: {step['result']}")
    print(f"Explicación: {step['explanation']}")
    print("-" * 50)
```

## Algoritmos Implementados

### Cálculo de Derivadas
1. **Primera Derivada**: Diferencias finitas centrales
   ```
   df/dt = (f(t+h) - f(t-h)) / (2h)
   ```

2. **Segunda Derivada**: Derivada de la primera derivada
   ```
   d²f/dt² = (f'(t+h) - f'(t-h)) / (2h)
   ```

### Modelo FFM
1. **Probabilidad de Falla**: Modelo de Weibull modificado
   ```
   P(falla) = 1 - exp(-λ * t^α)
   ```

2. **Tiempo hasta Falla**: Basado en aceleración
   ```
   t_falla = t_0 * exp(-β * aceleración)
   ```

### Detección de Precursores
- Clustering temporal de eventos
- Aceleración sísmica crítica
- Tendencias de intensidad creciente
- Variabilidad en patrones

## Configuración

### Umbrales de Alerta
Los umbrales se pueden configurar en `core/alert_system.py`:

```python
alert_thresholds = {
    AlertLevel.GREEN: {
        'probability_max': 0.15,
        'time_to_failure_min': 72,
    },
    AlertLevel.YELLOW: {
        'probability_max': 0.35,
        'time_to_failure_min': 48,
    },
    # ...
}
```

### Parámetros del Modelo FFM
Configurables en `core/ffm_model.py`:

```python
model_parameters = {
    'alpha': 1.0,    # Parámetro de forma
    'beta': 0.5,     # Parámetro de escala
    'gamma': 2.0     # Parámetro de aceleración
}
```

## Testing

```bash
# Ejecutar todos los tests
pytest

# Tests con cobertura
pytest --cov=core

# Tests específicos
pytest tests/test_derivatives.py -v
```

## Monitoreo y Logging

El sistema incluye logging detallado:
- Cálculos de derivadas
- Análisis FFM
- Generación de alertas
- Errores y excepciones

## API Export/Import

### Exportar Predicciones
```python
# El sistema puede exportar resultados en formato JSON
# Las predicciones incluyen todos los cálculos paso a paso
```

### Importar Datos Sísmicos
```python
# Acepta datos en formato estándar
# Compatible con formatos de monitoreo volcánico
```

## Extensibilidad

El sistema está diseñado para ser extensible:
- Nuevos algoritmos de derivadas
- Modelos FFM alternativos
- Sistemas de alerta personalizados
- Generadores de datos específicos

## Limitaciones

1. **No incluye integrales**: Solo derivadas como especificado
2. **Datos sintéticos**: Para demostración, no datos reales
3. **Modelo simplificado**: FFM básico, no considera todos los factores volcánicos
4. **Sin persistencia**: Los datos se mantienen en memoria

## Futuras Mejoras

- Integración con bases de datos
- Algoritmos de machine learning
- Visualización web interactiva
- Conectores para datos reales de sismógrafos
- Análisis multi-volcán

## ¿Por qué UV? 🚀

Este proyecto ha migrado a **UV** como gestor de paquetes por las siguientes ventajas:

### Velocidad Extrema
- **10-100x más rápido** que pip para instalar dependencias
- Instalación de todas las dependencias en **menos de 1 minuto**
- Cache inteligente que acelera reinstalaciones

### Gestión Automática
- **Entornos virtuales automáticos**: No necesitas activar/desactivar manualmente
- **Resolución inteligente**: Resuelve conflictos de dependencias automáticamente
- **Reproducibilidad garantizada**: Archivo `uv.lock` asegura builds idénticos

### Experiencia de Desarrollo Mejorada
- **Un solo comando**: `uv run python script.py` maneja todo automáticamente
- **Dependencias opcionales**: Instala solo lo que necesitas (básico/científico/desarrollo)
- **Compatible con pip**: Funciona con `requirements.txt` existentes

### Comandos Más Comunes

```powershell
# Configuración inicial
.\setup-dev.ps1                    # Script automático

# Gestión de dependencias
uv sync                             # Instalar dependencias
uv add requests                     # Añadir nueva dependencia
uv add --dev pytest-mock           # Añadir dependencia de desarrollo

# Ejecución
uv run python main.py               # Ejecutar servidor
uv run pytest                      # Ejecutar tests
uv run black .                      # Formatear código

# Información
uv --help                           # Ayuda completa
```

### Documentación Completa
Para información detallada sobre la implementación de UV:
📚 **[Guía de Implementación UV](../docs/UV_IMPLEMENTATION_GUIDE.md)**

---

## Contacto y Soporte

Este es un sistema de demostración para análisis de predicción volcánica mediante cálculo de derivadas y modelado FFM.
