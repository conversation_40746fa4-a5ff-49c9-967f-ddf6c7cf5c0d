/**
 * 🌋 Volcano App Frontend - Detector de Aceleración
 * Componente especializado para detectar y visualizar aceleración sísmica crítica
 */

import React, { useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Alert, AlertDescription } from '../ui/alert';
import { Badge } from '../ui/badge';
import { Progress } from '../ui/progress';
import { 
  AlertTriangle, 
  TrendingUp, 
  Activity, 
  Shield, 
  AlertOctagon,
  CheckCircle
} from 'lucide-react';
import { DerivativeResult } from '../../services/calculoService';

interface AccelerationDetectorProps {
  derivativeResult: DerivativeResult;
  className?: string;
}

interface AccelerationAnalysis {
  hasAcceleration: boolean;
  trend: 'increasing' | 'decreasing' | 'stable';
  severity: 'low' | 'medium' | 'high' | 'critical';
  riskScore: number;
  criticalPoints: number[];
  recommendedActions: string[];
  timeToAction: number;
}

export const AccelerationDetector: React.FC<AccelerationDetectorProps> = ({ 
  derivativeResult, 
  className 
}) => {
  
  // Análisis detallado de aceleración
  const analysis = useMemo((): AccelerationAnalysis => {
    const secondDerivative = derivativeResult.second_derivative;
    const positiveValues = secondDerivative.filter(val => val > 0);
    const hasAcceleration = positiveValues.length > secondDerivative.length * 0.4;
    
    // Analizar tendencia en los últimos 5 puntos
    const recentValues = secondDerivative.slice(-Math.min(5, secondDerivative.length));
    const increases = recentValues.slice(1).filter((val, i) => val > recentValues[i]).length;
    const decreases = recentValues.slice(1).filter((val, i) => val < recentValues[i]).length;
    
    let trend: 'increasing' | 'decreasing' | 'stable' = 'stable';
    if (increases > decreases * 1.5) trend = 'increasing';
    else if (decreases > increases * 1.5) trend = 'decreasing';
    
    // Calcular severidad y score de riesgo
    const maxAcceleration = Math.max(...secondDerivative);
    const avgAcceleration = positiveValues.reduce((sum, val) => sum + val, 0) / positiveValues.length || 0;
    
    let severity: 'low' | 'medium' | 'high' | 'critical' = 'low';
    let riskScore = 0;
    
    if (hasAcceleration && trend === 'increasing') {
      if (maxAcceleration > 5) {
        severity = 'critical';
        riskScore = 90 + Math.min(10, maxAcceleration - 5);
      } else if (maxAcceleration > 3) {
        severity = 'high';
        riskScore = 70 + (maxAcceleration - 3) * 10;
      } else if (maxAcceleration > 1) {
        severity = 'medium';
        riskScore = 40 + (maxAcceleration - 1) * 15;
      } else {
        severity = 'low';
        riskScore = 20 + maxAcceleration * 20;
      }
    } else if (hasAcceleration) {
      severity = 'medium';
      riskScore = Math.min(60, 30 + avgAcceleration * 10);
    } else {
      severity = 'low';
      riskScore = Math.min(30, avgAcceleration * 10);
    }
    
    // Identificar puntos críticos
    const criticalPoints = secondDerivative
      .map((val, index) => ({ value: val, index }))
      .filter(point => point.value > 2)
      .map(point => point.index);
    
    // Generar recomendaciones
    const recommendedActions: string[] = [];
    if (severity === 'critical') {
      recommendedActions.push('🚨 EVACUACIÓN INMEDIATA de zonas de riesgo');
      recommendedActions.push('📞 Activar protocolo de emergencia volcánica');
      recommendedActions.push('📡 Monitoreo continuo 24/7');
    } else if (severity === 'high') {
      recommendedActions.push('⚠️ Alerta temprana a poblaciones cercanas');
      recommendedActions.push('🔍 Intensificar monitoreo sísmico');
      recommendedActions.push('🏃 Preparar rutas de evacuación');
    } else if (severity === 'medium') {
      recommendedActions.push('📊 Aumentar frecuencia de monitoreo');
      recommendedActions.push('🔔 Mantener alerta amarilla');
      recommendedActions.push('📋 Revisar planes de contingencia');
    } else {
      recommendedActions.push('✅ Mantener monitoreo rutinario');
      recommendedActions.push('📈 Continuar análisis de tendencias');
    }
    
    // Calcular tiempo estimado para tomar acción
    const timeToAction = severity === 'critical' ? 0 : 
                        severity === 'high' ? 2 : 
                        severity === 'medium' ? 12 : 72;
    
    return {
      hasAcceleration,
      trend,
      severity,
      riskScore,
      criticalPoints,
      recommendedActions,
      timeToAction
    };
  }, [derivativeResult]);

  // Obtener color y icono según la severidad
  const getSeverityConfig = (severity: string) => {
    switch (severity) {
      case 'critical':
        return {
          color: 'text-red-600',
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          icon: AlertOctagon,
          label: 'CRÍTICO'
        };
      case 'high':
        return {
          color: 'text-orange-600',
          bgColor: 'bg-orange-50',
          borderColor: 'border-orange-200',
          icon: AlertTriangle,
          label: 'ALTO'
        };
      case 'medium':
        return {
          color: 'text-yellow-600',
          bgColor: 'bg-yellow-50',
          borderColor: 'border-yellow-200',
          icon: Activity,
          label: 'MEDIO'
        };
      default:
        return {
          color: 'text-green-600',
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200',
          icon: CheckCircle,
          label: 'BAJO'
        };
    }
  };

  const severityConfig = getSeverityConfig(analysis.severity);
  const IconComponent = severityConfig.icon;

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Alerta Principal */}
      <Alert className={`${severityConfig.bgColor} ${severityConfig.borderColor}`}>
        <IconComponent className={`h-5 w-5 ${severityConfig.color}`} />
        <AlertDescription>
          <div className="flex items-center justify-between">
            <div>
              <strong className={severityConfig.color}>
                Nivel de Riesgo: {severityConfig.label}
              </strong>
              <div className="text-sm mt-1">
                {analysis.hasAcceleration && analysis.trend === 'increasing' ? 
                  'Aceleración positiva y creciente detectada - Posible presurización del sistema magmático' :
                  analysis.hasAcceleration ?
                  'Aceleración detectada - Monitoreo intensivo requerido' :
                  'No se detecta aceleración crítica - Actividad dentro de parámetros normales'
                }
              </div>
            </div>
            <Badge variant={analysis.severity === 'critical' ? 'destructive' : 'outline'}>
              {analysis.riskScore}% riesgo
            </Badge>
          </div>
        </AlertDescription>
      </Alert>

      {/* Detalles del Análisis */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Análisis de Aceleración Sísmica
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Medidor de Riesgo */}
            <div>
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium">Score de Riesgo</span>
                <span className="text-sm font-bold">{analysis.riskScore}%</span>
              </div>
              <Progress 
                value={analysis.riskScore} 
                className="h-3"
              />
            </div>

            {/* Métricas Clave */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className={`text-2xl font-bold ${analysis.hasAcceleration ? 'text-red-600' : 'text-green-600'}`}>
                  {analysis.hasAcceleration ? 'SÍ' : 'NO'}
                </div>
                <div className="text-xs text-gray-600">Aceleración</div>
              </div>
              <div className="text-center">
                <div className={`text-2xl font-bold ${
                  analysis.trend === 'increasing' ? 'text-red-600' : 
                  analysis.trend === 'decreasing' ? 'text-green-600' : 'text-yellow-600'
                }`}>
                  {analysis.trend === 'increasing' ? '↗️' : 
                   analysis.trend === 'decreasing' ? '↘️' : '→'}
                </div>
                <div className="text-xs text-gray-600">Tendencia</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {analysis.criticalPoints.length}
                </div>
                <div className="text-xs text-gray-600">Puntos Críticos</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {analysis.timeToAction}h
                </div>
                <div className="text-xs text-gray-600">Tiempo p/Acción</div>
              </div>
            </div>

            {/* Interpretación Científica */}
            <div className="bg-blue-50 p-4 rounded-lg">
              <h4 className="font-semibold text-blue-800 mb-2 flex items-center gap-2">
                <Shield className="h-4 w-4" />
                Interpretación Científica
              </h4>
              <p className="text-sm text-blue-700">
                {analysis.hasAcceleration && analysis.trend === 'increasing' ? 
                  'La segunda derivada positiva y creciente (S\'\'(t) > 0 y en aumento) indica una aceleración en la liberación de energía sísmica. Según el modelo de Kilburn (2018), esto sugiere presurización del sistema magmático y puede ser una señal temprana de actividad volcánica inminente.' :
                  'Los datos actuales no muestran aceleración crítica. El sistema volcánico se mantiene en parámetros normales, pero se recomienda continuar con el monitoreo rutinario para detectar cambios en el patrón sísmico.'
                }
              </p>
            </div>

            {/* Recomendaciones */}
            <div className={`p-4 rounded-lg ${severityConfig.bgColor}`}>
              <h4 className={`font-semibold mb-2 ${severityConfig.color}`}>
                Acciones Recomendadas
              </h4>
              <ul className="space-y-1">
                {analysis.recommendedActions.map((action, index) => (
                  <li key={index} className={`text-sm ${severityConfig.color} flex items-start gap-2`}>
                    <span className="text-xs mt-1">•</span>
                    <span>{action}</span>
                  </li>
                ))}
              </ul>
            </div>

            {/* Fórmula de Detección */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="font-semibold text-gray-800 mb-2">Criterios de Detección</h4>
              <div className="text-sm text-gray-700 space-y-1">
                <div><strong>Aceleración Positiva:</strong> <code>S''(t) &gt; 0</code></div>
                <div><strong>Tendencia Creciente:</strong> <code>S''(t+1) &gt; S''(t)</code></div>
                <div><strong>Umbral Crítico:</strong> <code>S''(t) &gt; 2.0</code></div>
                <div><strong>Persistencia:</strong> <code>&gt;40% de puntos con aceleración positiva</code></div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AccelerationDetector;