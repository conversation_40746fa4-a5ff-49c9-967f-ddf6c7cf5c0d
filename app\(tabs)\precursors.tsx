/**
 * 🌋 Volcano App - Pantalla de Análisis de Precursores
 * Pantalla dedicada al análisis avanzado de precursores volcánicos
 */

import React, { useState, useCallback } from 'react';
import {
  StyleSheet,
  View,
  ScrollView,
  Alert,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Colors } from '@/constants/Colors';
import { Spacing, BorderRadius } from '@/constants/Layout';
import { AccessibleText } from '@/components/ui/AccessibleText';
import { PrimaryButton, SecondaryButton } from '@/components/ui/AccessibleButton';
import PrecursorAcceleration from '@/components/PrecursorAcceleration';
import {
  DatosPrecursor,
  ResultadoAlerta,
  DATOS_PRUEBA,
  UmbralesAlerta,
} from '@/types/precursor';

/**
 * Datos de ejemplo adicionales para demostración
 */
const DATOS_EJEMPLOS: DatosPrecursor[] = [
  DATOS_PRUEBA,
  {
    valores: [1, 1, 2, 2, 3, 4, 5, 7, 10, 15, 22],
    tipo: 'Deformación del Suelo',
    unidad: 'mm/día',
    volcan: 'Volcán Villarrica',
  },
  {
    valores: [5, 6, 7, 8, 10, 13, 17, 22, 28, 35, 43],
    tipo: 'Emisiones de SO2',
    unidad: 'toneladas/día',
    volcan: 'Volcán Villarrica',
  },
  {
    valores: [0.1, 0.2, 0.3, 0.5, 0.8, 1.3, 2.1, 3.4, 5.5, 8.9, 14.4],
    tipo: 'Temperatura Fumarolas',
    unidad: '°C incremento',
    volcan: 'Volcán Villarrica',
  },
];

export default function PrecursorsScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];

  // Estado de la pantalla
  const [datosActuales, setDatosActuales] = useState<DatosPrecursor>(DATOS_PRUEBA);
  const [ejemploSeleccionado, setEjemploSeleccionado] = useState<number>(0);
  const [umbralesPersonalizados, setUmbralesPersonalizados] = useState<UmbralesAlerta>({
    verde: 1,
    amarillo: 5,
    rojo: 5,
  });

  // Manejar alertas del componente
  const handleAlerta = useCallback((resultado: ResultadoAlerta) => {
    const titulo = `Alerta de Precursores: ${resultado.nivel}`;
    const mensaje = `${resultado.mensaje}\n\nValor detectado: ${resultado.valor?.toFixed(2) || 'N/A'}`;

    if (Platform.OS === 'web') {
      alert(`${titulo}\n\n${mensaje}`);
    } else {
      Alert.alert(titulo, mensaje, [
        { text: 'Entendido', style: 'default' },
      ]);
    }
  }, []);

  // Cambiar ejemplo de datos
  const cambiarEjemplo = useCallback((indice: number) => {
    if (indice >= 0 && indice < DATOS_EJEMPLOS.length) {
      setEjemploSeleccionado(indice);
      setDatosActuales(DATOS_EJEMPLOS[indice]);
    }
  }, []);

  // Renderizar selector de ejemplos
  const renderSelectorEjemplos = () => (
    <View style={styles.selectorContainer}>
      <AccessibleText variant="h4" style={styles.selectorTitulo}>
        Ejemplos de Datos
      </AccessibleText>
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        style={styles.selectorScroll}
      >
        {DATOS_EJEMPLOS.map((ejemplo, indice) => (
          <SecondaryButton
            key={indice}
            onPress={() => cambiarEjemplo(indice)}
            style={[
              styles.botonEjemplo,
              ejemploSeleccionado === indice && styles.botonEjemploActivo
            ]}
          >
            {ejemplo.tipo}
          </SecondaryButton>
        ))}
      </ScrollView>
    </View>
  );

  // Renderizar información del dataset actual
  const renderInfoDataset = () => (
    <View style={styles.infoContainer}>
      <AccessibleText variant="h4" style={styles.infoTitulo}>
        Dataset Actual
      </AccessibleText>
      <View style={styles.infoGrid}>
        <View style={styles.infoItem}>
          <AccessibleText variant="caption">Tipo:</AccessibleText>
          <AccessibleText variant="body">{datosActuales.tipo}</AccessibleText>
        </View>
        <View style={styles.infoItem}>
          <AccessibleText variant="caption">Unidad:</AccessibleText>
          <AccessibleText variant="body">{datosActuales.unidad}</AccessibleText>
        </View>
        <View style={styles.infoItem}>
          <AccessibleText variant="caption">Puntos de Datos:</AccessibleText>
          <AccessibleText variant="body">{datosActuales.valores.length}</AccessibleText>
        </View>
        <View style={styles.infoItem}>
          <AccessibleText variant="caption">Volcán:</AccessibleText>
          <AccessibleText variant="body">{datosActuales.volcan}</AccessibleText>
        </View>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView 
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {/* Header */}
        <View style={styles.header}>
          <AccessibleText variant="h1" style={styles.titulo}>
            🌋 Análisis de Precursores
          </AccessibleText>
          <AccessibleText variant="body" color="muted" style={styles.subtitulo}>
            Sistema avanzado de análisis matemático para detección temprana de actividad volcánica
          </AccessibleText>
        </View>

        {/* Selector de ejemplos */}
        {renderSelectorEjemplos()}

        {/* Información del dataset */}
        {renderInfoDataset()}

        {/* Componente principal de análisis */}
        <View style={styles.analisisContainer}>
          <PrecursorAcceleration
            datos={datosActuales}
            umbrales={umbralesPersonalizados}
            onAlerta={handleAlerta}
            titulo="Análisis de Aceleración"
            altura={250}
            mostrarControles={true}
          />
        </View>

        {/* Información adicional */}
        <View style={styles.infoAdicional}>
          <AccessibleText variant="h4" style={styles.infoTitulo}>
            ℹ️ Cómo Funciona
          </AccessibleText>
          <AccessibleText variant="body" style={styles.infoTexto}>
            Este sistema analiza la <AccessibleText variant="body" style={styles.textoBold}>tasa de cambio</AccessibleText> (primera derivada) 
            y la <AccessibleText variant="body" style={styles.textoBold}>aceleración</AccessibleText> (segunda derivada) de los datos de precursores volcánicos.
          </AccessibleText>
          <AccessibleText variant="body" style={styles.infoTexto}>
            • <AccessibleText variant="body" style={styles.textoVerde}>Verde (≤ 1)</AccessibleText>: Actividad estable
          </AccessibleText>
          <AccessibleText variant="body" style={styles.infoTexto}>
            • <AccessibleText variant="body" style={styles.textoAmarillo}>Amarillo (1-5)</AccessibleText>: Precaución, actividad acelerando
          </AccessibleText>
          <AccessibleText variant="body" style={styles.infoTexto}>
            • <AccessibleText variant="body" style={styles.textoRojo}>Rojo (> 5)</AccessibleText>: Alerta, aceleración peligrosa
          </AccessibleText>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },

  scrollView: {
    flex: 1,
  },

  scrollContent: {
    padding: Spacing.md,
  },

  header: {
    alignItems: 'center',
    marginBottom: Spacing.xl,
  },

  titulo: {
    textAlign: 'center',
    marginBottom: Spacing.sm,
  },

  subtitulo: {
    textAlign: 'center',
    paddingHorizontal: Spacing.md,
  },

  selectorContainer: {
    marginBottom: Spacing.lg,
  },

  selectorTitulo: {
    marginBottom: Spacing.md,
    textAlign: 'center',
  },

  selectorScroll: {
    flexGrow: 0,
  },

  botonEjemplo: {
    marginRight: Spacing.sm,
    paddingHorizontal: Spacing.md,
    minWidth: 120,
  },

  botonEjemploActivo: {
    backgroundColor: '#3b82f6',
  },

  infoContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: BorderRadius.lg,
    padding: Spacing.md,
    marginBottom: Spacing.lg,
  },

  infoTitulo: {
    textAlign: 'center',
    marginBottom: Spacing.md,
    fontWeight: '600',
  },

  infoGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },

  infoItem: {
    width: '48%',
    marginBottom: Spacing.sm,
  },

  analisisContainer: {
    marginBottom: Spacing.xl,
  },

  infoAdicional: {
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: BorderRadius.lg,
    padding: Spacing.md,
    marginBottom: Spacing.xl,
  },

  infoTexto: {
    marginBottom: Spacing.sm,
    lineHeight: 20,
  },

  textoBold: {
    fontWeight: 'bold',
  },

  textoVerde: {
    color: '#22c55e',
    fontWeight: 'bold',
  },

  textoAmarillo: {
    color: '#eab308',
    fontWeight: 'bold',
  },

  textoRojo: {
    color: '#ef4444',
    fontWeight: 'bold',
  },
});
