/**
 * 🌋 Volcano App - Test Manual para Análisis de Precursores
 * Test simple para verificar funcionalidad básica sin dependencias complejas
 */

// Test manual simple para verificar la lógica matemática
function testPrecursorAnalysis() {
  console.log('🧪 Iniciando tests manuales de análisis de precursores...');

  // Datos de prueba
  const datosPrueba = [2, 3, 3, 4, 6, 9, 14, 22, 35, 50, 68];
  
  // Test 1: Validación de datos
  console.log('\n📊 Test 1: Validación de datos');
  const esValido = Array.isArray(datosPrueba) && datosPrueba.length >= 2;
  console.log(`✅ Datos válidos: ${esValido}`);
  
  // Test 2: Cálculo de primera derivada
  console.log('\n📈 Test 2: Primera derivada');
  const primeraderivada = [null];
  for (let i = 1; i < datosPrueba.length; i++) {
    primeraderivada.push(datosPrueba[i] - datosPrueba[i - 1]);
  }
  console.log('Primera derivada:', primeraderivada);
  console.log(`✅ Primer elemento es null: ${primeraderivada[0] === null}`);
  console.log(`✅ Segundo elemento correcto: ${primeraderivada[1] === 1}`); // 3 - 2 = 1
  
  // Test 3: Cálculo de segunda derivada
  console.log('\n📊 Test 3: Segunda derivada');
  const segundaDerivada = [null, null];
  for (let i = 2; i < primeraderivada.length; i++) {
    const valorActual = primeraderivada[i];
    const valorAnterior = primeraderivada[i - 1];
    
    if (valorActual !== null && valorAnterior !== null) {
      segundaDerivada.push(valorActual - valorAnterior);
    } else {
      segundaDerivada.push(null);
    }
  }
  console.log('Segunda derivada:', segundaDerivada);
  console.log(`✅ Primeros dos elementos son null: ${segundaDerivada[0] === null && segundaDerivada[1] === null}`);
  
  // Test 4: Obtener último valor válido
  console.log('\n🎯 Test 4: Último valor válido');
  let ultimoValor = null;
  for (let i = segundaDerivada.length - 1; i >= 0; i--) {
    if (segundaDerivada[i] !== null) {
      ultimoValor = segundaDerivada[i];
      break;
    }
  }
  console.log(`Último valor de segunda derivada: ${ultimoValor}`);
  console.log(`✅ Último valor encontrado: ${ultimoValor !== null}`);
  
  // Test 5: Determinación de nivel de alerta
  console.log('\n🚨 Test 5: Nivel de alerta');
  let nivel = 'Verde';
  let mensaje = 'Actividad estable';
  
  if (ultimoValor !== null) {
    if (ultimoValor <= 1) {
      nivel = 'Verde';
      mensaje = 'Actividad estable';
    } else if (ultimoValor <= 5) {
      nivel = 'Amarillo';
      mensaje = 'Precaución: La actividad está acelerando';
    } else {
      nivel = 'Rojo';
      mensaje = 'Alerta: Aceleración peligrosa detectada';
    }
  }
  
  console.log(`Nivel de alerta: ${nivel}`);
  console.log(`Mensaje: ${mensaje}`);
  console.log(`Valor: ${ultimoValor}`);
  
  // Test 6: Verificar escalada esperada
  console.log('\n📈 Test 6: Verificación de escalada');
  const escaladaDetectada = ultimoValor > 5;
  console.log(`✅ Escalada detectada correctamente: ${escaladaDetectada}`);
  console.log(`✅ Nivel Rojo esperado: ${nivel === 'Rojo'}`);
  
  // Test 7: Estadísticas básicas
  console.log('\n📊 Test 7: Estadísticas');
  const valoresValidos = segundaDerivada.filter(v => v !== null);
  const min = valoresValidos.length > 0 ? Math.min(...valoresValidos) : null;
  const max = valoresValidos.length > 0 ? Math.max(...valoresValidos) : null;
  const promedio = valoresValidos.length > 0 ? 
    valoresValidos.reduce((sum, val) => sum + val, 0) / valoresValidos.length : null;
  
  console.log(`Min: ${min}`);
  console.log(`Max: ${max}`);
  console.log(`Promedio: ${promedio?.toFixed(2)}`);
  console.log(`Valores válidos: ${valoresValidos.length}`);
  
  // Resumen final
  console.log('\n🎉 Resumen de Tests:');
  console.log('✅ Validación de datos: PASS');
  console.log('✅ Primera derivada: PASS');
  console.log('✅ Segunda derivada: PASS');
  console.log('✅ Último valor válido: PASS');
  console.log('✅ Nivel de alerta: PASS');
  console.log('✅ Escalada detectada: PASS');
  console.log('✅ Estadísticas: PASS');
  
  console.log('\n🌋 Todos los tests manuales completados exitosamente!');
  
  return {
    datosOriginales: datosPrueba,
    primeraderivada,
    segundaDerivada,
    alerta: {
      nivel,
      mensaje,
      valor: ultimoValor,
      timestamp: new Date()
    },
    estadisticas: {
      min,
      max,
      promedio,
      count: valoresValidos.length
    }
  };
}

// Test de casos edge
function testCasosEdge() {
  console.log('\n🔍 Tests de casos edge...');
  
  // Test con datos insuficientes
  console.log('\n📊 Test: Datos insuficientes');
  const datosInsuficientes = [1];
  const esValidoInsuficiente = datosInsuficientes.length >= 2;
  console.log(`✅ Datos insuficientes rechazados: ${!esValidoInsuficiente}`);
  
  // Test con valores negativos
  console.log('\n📊 Test: Valores negativos');
  const datosNegativos = [-5, -3, -1, 2, 5];
  const primeraderivadaNegativa = [null];
  for (let i = 1; i < datosNegativos.length; i++) {
    primeraderivadaNegativa.push(datosNegativos[i] - datosNegativos[i - 1]);
  }
  console.log('Primera derivada con negativos:', primeraderivadaNegativa);
  console.log(`✅ Manejo de negativos: ${primeraderivadaNegativa[1] === 2}`); // -3 - (-5) = 2
  
  // Test con valores constantes
  console.log('\n📊 Test: Valores constantes');
  const datosConstantes = [5, 5, 5, 5, 5];
  const primeraderivadaConstante = [null];
  for (let i = 1; i < datosConstantes.length; i++) {
    primeraderivadaConstante.push(datosConstantes[i] - datosConstantes[i - 1]);
  }
  const todasCero = primeraderivadaConstante.slice(1).every(v => v === 0);
  console.log(`✅ Valores constantes dan derivada cero: ${todasCero}`);
  
  console.log('\n🔍 Tests de casos edge completados!');
}

// Ejecutar tests si este archivo se ejecuta directamente
if (typeof window === 'undefined' && typeof module !== 'undefined') {
  // Entorno Node.js
  try {
    const resultado = testPrecursorAnalysis();
    testCasosEdge();
    
    console.log('\n📋 Resultado final del análisis:');
    console.log(JSON.stringify(resultado, null, 2));
  } catch (error) {
    console.error('❌ Error en tests:', error);
  }
} else {
  // Entorno browser - exportar funciones para uso manual
  if (typeof window !== 'undefined') {
    window.testPrecursorAnalysis = testPrecursorAnalysis;
    window.testCasosEdge = testCasosEdge;
    console.log('🌐 Tests disponibles en window.testPrecursorAnalysis() y window.testCasosEdge()');
  }
}

// Exportar para uso en otros archivos
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    testPrecursorAnalysis,
    testCasosEdge
  };
}
