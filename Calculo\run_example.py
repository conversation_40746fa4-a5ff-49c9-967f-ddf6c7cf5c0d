#!/usr/bin/env python3
"""
Ejemplo de uso del Sistema de Predicción Volcánica
Demuestra todas las funcionalidades principales
"""

import asyncio
import json
from datetime import datetime, <PERSON><PERSON><PERSON>
from typing import List

from core.derivatives import DerivativeCalculator
from core.ffm_model import FFMModel
from core.data_generator import SeismicDataGenerator
from core.alert_system import AlertSystem
from models.volcanic_data import SeismicReading, AlertLevel

def print_separator(title: str):
    """Imprime un separador visual"""
    print("\n" + "="*60)
    print(f"  {title}")
    print("="*60)

def print_calculation_steps(steps: List[dict], max_steps: int = 5):
    """Imprime los pasos de cálculo"""
    print(f"\n📊 Pasos de Cálculo (mostrando {min(len(steps), max_steps)} de {len(steps)}):")
    for i, step in enumerate(steps[:max_steps]):
        print(f"\n  Paso {step['step_number']}: {step['description']}")
        print(f"  Fórmula: {step['formula']}")
        print(f"  Cálculo: {step['calculation']}")
        print(f"  Resultado: {step['result']:.4f}")
        print(f"  Explicación: {step['explanation']}")

def demonstrate_data_generation():
    """Demuestra la generación de datos sintéticos"""
    print_separator("GENERACIÓN DE DATOS SINTÉTICOS")
    
    generator = SeismicDataGenerator(seed=42)
    
    # Generar datos normales
    print("\n🌋 Generando datos de actividad normal (3 días)...")
    normal_data = generator.generate_seismic_sequence(
        days=3, 
        eruption_scenario=False
    )
    print(f"   Eventos generados: {len(normal_data)}")
    print(f"   Primer evento: {normal_data[0].timestamp} - Magnitud: {normal_data[0].magnitude:.2f}")
    print(f"   Último evento: {normal_data[-1].timestamp} - Magnitud: {normal_data[-1].magnitude:.2f}")
    
    # Generar datos pre-erupción
    print("\n🌋 Generando datos pre-erupción (5 días)...")
    eruption_data = generator.generate_seismic_sequence(
        days=5, 
        eruption_scenario=True
    )
    print(f"   Eventos generados: {len(eruption_data)}")
    
    # Estadísticas
    magnitudes = [event.magnitude for event in eruption_data]
    print(f"   Magnitud promedio: {sum(magnitudes)/len(magnitudes):.2f}")
    print(f"   Magnitud máxima: {max(magnitudes):.2f}")
    print(f"   Magnitud mínima: {min(magnitudes):.2f}")
    
    return normal_data, eruption_data

def demonstrate_derivative_calculation(seismic_data: List[SeismicReading]):
    """Demuestra el cálculo de derivadas"""
    print_separator("CÁLCULO DE DERIVADAS")
    
    calculator = DerivativeCalculator()
    
    print(f"\n📈 Calculando derivadas para {len(seismic_data)} puntos de datos...")
    
    # Calcular con pasos detallados
    result = calculator.calculate_derivatives(
        seismic_data, 
        show_steps=True
    )
    
    print(f"\n📊 Resultados del análisis:")
    print(f"   Aceleración máxima: {result.max_acceleration:.4f}")
    print(f"   Tendencia: {result.acceleration_trend}")
    print(f"   Puntos de aceleración detectados: {len([p for p in result.acceleration_points if p != 0])}")
    
    # Mostrar algunos pasos de cálculo
    if result.calculation_steps:
        print_calculation_steps(result.calculation_steps)
    
    return result

def demonstrate_ffm_analysis(seismic_data: List[SeismicReading], derivative_result):
    """Demuestra el análisis FFM"""
    print_separator("ANÁLISIS FFM (FAILURE FORECAST MODEL)")
    
    ffm_model = FFMModel()
    
    print(f"\n🔮 Aplicando modelo FFM...")
    
    ffm_result = ffm_model.analyze_failure_pattern(
        seismic_data, 
        derivative_result
    )
    
    print(f"\n📊 Resultados del análisis FFM:")
    print(f"   Probabilidad de falla: {ffm_result.failure_probability:.2%}")
    print(f"   Tiempo estimado hasta falla: {ffm_result.time_to_failure:.1f} horas" if ffm_result.time_to_failure else "   Tiempo hasta falla: No determinado")
    print(f"   Tendencia de aceleración: {ffm_result.acceleration_trend}")
    print(f"   Nivel de confianza: {ffm_result.confidence_level:.2%}")
    print(f"   Umbral crítico alcanzado: {ffm_result.critical_threshold:.2%}")
    
    if ffm_result.precursor_indicators:
        print(f"\n🚨 Indicadores precursores detectados:")
        for indicator in ffm_result.precursor_indicators:
            print(f"   • {indicator}")
    
    return ffm_result

def demonstrate_alert_system(ffm_result):
    """Demuestra el sistema de alertas"""
    print_separator("SISTEMA DE ALERTAS TEMPRANAS")
    
    alert_system = AlertSystem()
    
    print(f"\n🚨 Evaluando nivel de riesgo...")
    
    alert_level = alert_system.evaluate_risk(ffm_result)
    
    print(f"\n📢 Nivel de alerta determinado: {alert_level.value}")
    
    # Obtener alertas activas
    active_alerts = alert_system.get_active_alerts()
    
    if active_alerts:
        alert = active_alerts[0]
        print(f"\n🚨 Alerta activa:")
        print(f"   ID: {alert.id}")
        print(f"   Nivel: {alert.level.value}")
        print(f"   Mensaje: {alert.message}")
        print(f"   Probabilidad: {alert.probability:.2%}")
        print(f"   Timestamp: {alert.timestamp}")
        
        print(f"\n📋 Recomendaciones:")
        for i, rec in enumerate(alert.recommendations, 1):
            print(f"   {i}. {rec}")
    
    # Estadísticas del sistema
    stats = alert_system.get_alert_statistics()
    print(f"\n📊 Estadísticas del sistema de alertas:")
    print(f"   Total de alertas: {stats['total_alerts']}")
    print(f"   Alertas activas: {stats['active_alerts']}")
    print(f"   Alertas últimas 24h: {stats['last_24h']['total']}")
    
    return alert_level

def demonstrate_pattern_analysis():
    """Demuestra análisis de patrones específicos"""
    print_separator("ANÁLISIS DE PATRONES ESPECÍFICOS")
    
    generator = SeismicDataGenerator(seed=123)
    calculator = DerivativeCalculator()
    
    patterns = [
        ("linear_increase", "Aumento Lineal"),
        ("exponential_increase", "Aumento Exponencial"),
        ("periodic", "Patrón Periódico"),
        ("random_bursts", "Ráfagas Aleatorias")
    ]
    
    for pattern_type, pattern_name in patterns:
        print(f"\n🔍 Analizando patrón: {pattern_name}")
        
        # Generar datos del patrón
        pattern_data = generator.generate_pattern_data(
            pattern_type=pattern_type,
            hours=24
        )
        
        # Calcular derivadas
        result = calculator.calculate_derivatives(pattern_data, show_steps=False)
        
        print(f"   Eventos generados: {len(pattern_data)}")
        print(f"   Aceleración máxima: {result.max_acceleration:.4f}")
        print(f"   Tendencia: {result.acceleration_trend}")

def demonstrate_real_time_simulation():
    """Demuestra simulación en tiempo real"""
    print_separator("SIMULACIÓN EN TIEMPO REAL")
    
    generator = SeismicDataGenerator()
    calculator = DerivativeCalculator()
    ffm_model = FFMModel()
    alert_system = AlertSystem()
    
    print(f"\n⏱️ Simulando monitoreo en tiempo real...")
    
    # Simular datos acumulativos
    accumulated_data = []
    scenarios = ["normal", "normal", "escalated", "escalated", "critical"]
    
    for i, scenario in enumerate(scenarios):
        print(f"\n   Hora {i+1}: Escenario {scenario}")
        
        # Generar nuevo evento
        new_event = generator.generate_real_time_data(
            scenario_type=scenario,
            timestamp=datetime.utcnow() + timedelta(hours=i)
        )
        
        accumulated_data.append(new_event)
        
        if len(accumulated_data) >= 3:  # Necesitamos al menos 3 puntos
            # Calcular derivadas
            deriv_result = calculator.calculate_derivatives(
                accumulated_data[-10:], show_steps=False  # Últimos 10 puntos
            )
            
            # Análisis FFM
            ffm_result = ffm_model.analyze_failure_pattern(
                accumulated_data[-10:], deriv_result
            )
            
            # Evaluación de alerta
            alert_level = alert_system.evaluate_risk(ffm_result)
            
            print(f"     Magnitud: {new_event.magnitude:.2f}")
            print(f"     Probabilidad: {ffm_result.failure_probability:.2%}")
            print(f"     Alerta: {alert_level.value}")

def main():
    """Función principal de demostración"""
    print("🌋 SISTEMA DE PREDICCIÓN VOLCÁNICA - DEMOSTRACIÓN")
    print("   Cálculo de derivadas y modelo FFM para predicción de erupciones")
    print("   Desarrollado como monolito modular independiente")
    
    try:
        # 1. Generar datos sintéticos
        normal_data, eruption_data = demonstrate_data_generation()
        
        # 2. Usar datos de erupción para análisis completo
        derivative_result = demonstrate_derivative_calculation(eruption_data)
        
        # 3. Análisis FFM
        ffm_result = demonstrate_ffm_analysis(eruption_data, derivative_result)
        
        # 4. Sistema de alertas
        alert_level = demonstrate_alert_system(ffm_result)
        
        # 5. Análisis de patrones
        demonstrate_pattern_analysis()
        
        # 6. Simulación tiempo real
        demonstrate_real_time_simulation()
        
        print_separator("RESUMEN FINAL")
        print(f"\n✅ Demostración completada exitosamente")
        print(f"   Datos procesados: {len(eruption_data)} eventos sísmicos")
        print(f"   Probabilidad final de erupción: {ffm_result.failure_probability:.2%}")
        print(f"   Nivel de alerta final: {alert_level.value}")
        print(f"   Tiempo estimado hasta falla: {ffm_result.time_to_failure:.1f} horas" if ffm_result.time_to_failure else "   Tiempo hasta falla: No determinado")
        
        print(f"\n🚀 Para usar el sistema como API, ejecute:")
        print(f"   python main.py")
        print(f"   Luego visite: http://localhost:8000/docs")
        
    except Exception as e:
        print(f"\n❌ Error durante la demostración: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()