# Integración Frontend React - Cálculo Volcánico

## Resumen

Este documento describe la integración exitosa del monolito Python de cálculo volcánico con el frontend React de la aplicación Volcano App. La integración permite visualizar análisis de derivadas sísmicas y detección de aceleración volcánica en tiempo real.

## Fecha de Implementación

**Fecha:** 11 de enero de 2025  
**Implementado por:** Claude Code  
**Estado:** ✅ Completado y funcional

## Arquitectura de la Integración

```
┌─────────────────────────────────────────────────────────────────┐
│                    Frontend React                               │
│  ┌─────────────────────────────────────────────────────────────┐│
│  │               CalculoVolcanico.tsx                          ││
│  │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐││
│  │  │ DerivativeChart │  │AccelerationDete-│  │ calculoService  │││
│  │  │     Component   │  │    tor Component│  │     (API)       │││
│  │  └─────────────────┘  └─────────────────┘  └─────────────────┘││
│  └─────────────────────────────────────────────────────────────┘│
└─────────────────────────────────────────────────────────────────┘
                                  │
                                  │ HTTP/REST API
                                  │
┌─────────────────────────────────────────────────────────────────┐
│                  Monolito Python                                │
│  ┌─────────────────────────────────────────────────────────────┐│
│  │                   FastAPI Server                            ││
│  │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐││
│  │  │ /data/generate  │  │/calculate/deriv-│  │   /predict      │││
│  │  │   Endpoint      │  │   atives End.   │  │   Endpoint      │││
│  │  └─────────────────┘  └─────────────────┘  └─────────────────┘││
│  └─────────────────────────────────────────────────────────────┘│
└─────────────────────────────────────────────────────────────────┘
```

## Componentes Creados

### 1. CalculoVolcanico.tsx (Vista Principal)
**Ubicación:** `/backoffice/frontend/src/components/calculo/CalculoVolcanico.tsx`

**Funcionalidades:**
- Control de generación de datos sintéticos
- Interfaz para configurar escenarios (Normal/Pre-erupción)
- Gestión de estados de carga y errores
- Integración con el servicio de cálculo Python
- Visualización de resultados en pestañas (Gráficos, Detector, Análisis, Teoría)

**Características clave:**
- Verificación de salud del servicio Python
- Configuración de parámetros (días, escenarios, mostrar pasos)
- Manejo de errores con notificaciones toast
- Respuesta reactiva para diferentes tamaños de pantalla

### 2. DerivativeChart.tsx (Visualización de Gráficos)
**Ubicación:** `/backoffice/frontend/src/components/calculo/DerivativeChart.tsx`

**Funcionalidades:**
- Gráfico interactivo de primera y segunda derivada
- Detección visual de zonas de aceleración
- Área chart para highlighting de aceleración positiva
- Pasos de cálculo detallados (opcional)

**Librerías utilizadas:**
- Recharts para visualización de datos
- Tooltips personalizados para detalles técnicos
- Códigos de color científicos para diferentes tipos de datos

### 3. AccelerationDetector.tsx (Detector de Aceleración)
**Ubicación:** `/backoffice/frontend/src/components/calculo/AccelerationDetector.tsx`

**Funcionalidades:**
- Análisis automático de riesgo volcánico
- Detección de aceleración positiva y tendencias
- Sistema de alertas por niveles (Bajo, Medio, Alto, Crítico)
- Recomendaciones automatizadas basadas en el análisis

**Criterios de detección:**
- Aceleración Positiva: `S''(t) > 0`
- Tendencia Creciente: `S''(t+1) > S''(t)`
- Umbral Crítico: `S''(t) > 2.0`
- Persistencia: `>40% de puntos con aceleración positiva`

### 4. calculoService.ts (Cliente API)
**Ubicación:** `/backoffice/frontend/src/services/calculoService.ts`

**Funcionalidades:**
- Cliente HTTP para comunicación con la API Python
- Métodos para generar datos sintéticos
- Cálculo de derivadas desde datos sísmicos
- Predicción de erupciones volcánicas
- Manejo de errores y timeouts

**Endpoints integrados:**
- `GET /health` - Verificación de salud del servicio
- `GET /data/generate` - Generación de datos sintéticos
- `POST /calculate/derivatives` - Cálculo de derivadas
- `POST /predict` - Predicción de erupciones

## Configuración y Variables de Entorno

### Frontend (.env)
```bash
# Configuración del API de cálculo Python
VITE_CALCULO_API_BASE_URL=http://localhost:8000
```

### Integración en el Router
**Archivo:** `/backoffice/frontend/src/App.tsx`
- Agregada nueva ruta 'calculo' al sistema de navegación
- Importación del componente CalculoVolcanico
- Integración con el layout principal de la aplicación

### Navegación
**Archivo:** `/backoffice/frontend/src/components/layout/AppLayout.tsx`
- Nuevo ítem de navegación "Cálculo Volcánico"
- Ícono: TrendingUp de Lucide React
- Descripción: "Análisis de derivadas"

## Componentes UI Creados

### Alert Component
**Ubicación:** `/backoffice/frontend/src/components/ui/alert.tsx`

**Problema resuelto:** Faltaba el componente Alert en la librería shadcn/ui local

**Funcionalidades:**
- Componente base para alertas y notificaciones
- Variantes: default, destructive
- Componentes exportados: Alert, AlertTitle, AlertDescription

## Interfaces TypeScript

### Principales Interfaces Creadas:

```typescript
// Datos sintéticos - Estructura real de la API Python
interface SyntheticDataResponse {
  data: SeismicDataPoint[];
  count: number;
  period_days: number;
  eruption_scenario: boolean;
  generated_at: string;
}

// Resultado de derivadas - Estructura real de la API Python
interface DerivativeResult {
  first_derivative: number[];
  second_derivative: number[];
  acceleration_points: number[];
  calculation_steps: CalculationStep[] | null;
  timestamp: string;
}

// Punto de datos sísmicos
interface SeismicDataPoint {
  timestamp: string;
  magnitude: number;
  frequency: number;
  duration: number;
  depth: number;
}
```

## Problemas Resueltos

### 1. Error: `syntheticData.metadata is undefined`
**Causa:** Discrepancia entre la estructura esperada por el frontend y la respuesta real de la API Python.

**Solución:** Actualización de la interfaz `SyntheticDataResponse` para coincidir con la estructura real:
```typescript
// Antes (erróneo)
metadata: {
  total_points: number;
  scenario: string;
  pattern_applied: string;
  time_range: { start: string; end: string; };
}

// Después (correcto)
count: number;
period_days: number;
eruption_scenario: boolean;
generated_at: string;
```

### 2. Error: `derivativeResult.time_points is undefined`
**Causa:** La API Python no devuelve `time_points` ni `original_data`.

**Solución:** 
- Generación local de puntos de tiempo basándose en índices
- Actualización de la interfaz `DerivativeResult`
- Modificación del componente para trabajar solo con derivadas

### 3. Error: `derivativeResult.original_data is undefined`
**Causa:** Referencias a propiedades no existentes en la respuesta de la API.

**Solución:** Reemplazo de `derivativeResult.original_data.length` por `derivativeResult.first_derivative.length`

### 4. Error: Propiedades de análisis no definidas
**Causa:** `acceleration_detected` y `acceleration_trend` no están en la respuesta de la API.

**Solución:** Implementación de cálculo local de estas propiedades:
```typescript
const accelerationDetected = accelerationZones.length > 0;
const trend = recentValues.length > 1 && 
  recentValues[recentValues.length - 1] > recentValues[0] ? 'increasing' : 'decreasing';
```

## Funcionalidades Implementadas

### 1. Generación de Datos Sintéticos
- Escenarios configurables (Normal/Pre-erupción)
- Períodos de tiempo variables (3, 7, 14, 30 días)
- Diferentes patrones de datos (Lineal/Exponencial)

### 2. Cálculo de Derivadas
- Primera derivada: `S'(t) = S(t) - S(t-1)`
- Segunda derivada: `S''(t) = S(t) - 2S(t-1) + S(t-2)`
- Pasos de cálculo detallados (opcional)

### 3. Visualización Interactiva
- Gráficos de líneas para derivadas
- Área chart para zonas de aceleración
- Tooltips informativos
- Leyendas científicas

### 4. Análisis de Riesgo
- Detección automática de aceleración
- Clasificación por niveles de riesgo
- Recomendaciones automatizadas
- Interpretación científica

### 5. Predicción de Erupciones
- Modelo FFM (Failure Forecast Model)
- Probabilidad de erupción
- Tiempo estimado hasta la falla
- Análisis de tendencias

## Fundamentos Científicos

### Modelo de Pronóstico de Falla (FFM)
Basado en el trabajo de Kilburn (2018), el sistema detecta:
- Aceleración en la liberación de energía sísmica
- Presurización del sistema magmático
- Señales tempranas de actividad volcánica

### Criterios de Detección Crítica
- **Segunda derivada positiva:** `S''(t) > 0`
- **Tendencia creciente:** `S''(t+1) > S''(t)`
- **Umbral crítico:** `S''(t) > 2.0`
- **Persistencia:** `>40% de puntos con aceleración positiva`

### Niveles de Alerta
- **Verde (LOW):** Actividad normal
- **Amarillo (MEDIUM):** Monitoreo intensivo
- **Naranja (HIGH):** Alerta temprana
- **Rojo (CRITICAL):** Evacuación inmediata

## Estructura de Archivos Creados

```
backoffice/frontend/src/
├── components/
│   ├── calculo/
│   │   ├── CalculoVolcanico.tsx          # Vista principal
│   │   ├── DerivativeChart.tsx           # Gráficos interactivos
│   │   └── AccelerationDetector.tsx      # Detector de aceleración
│   ├── layout/
│   │   └── AppLayout.tsx                 # Navegación actualizada
│   └── ui/
│       └── alert.tsx                     # Componente Alert creado
├── services/
│   └── calculoService.ts                 # Cliente API Python
└── App.tsx                               # Routing actualizado
```

## Configuración de Desarrollo

### Comandos para Iniciar la Integración

1. **Iniciar el monolito Python:**
   ```bash
   cd /Calculo
   python -m uvicorn main:app --reload --host 0.0.0.0 --port 8000
   ```

2. **Iniciar el frontend React:**
   ```bash
   cd /backoffice/frontend
   npm run dev
   ```

3. **Acceder a la funcionalidad:**
   - URL: `http://localhost:5173`
   - Navegar a "Cálculo Volcánico" en el menú lateral

## Testing y Validación

### Scenarios de Prueba Implementados

1. **Escenario Normal (7 días):**
   - Actividad sísmica regular
   - Derivadas estables
   - Riesgo bajo

2. **Escenario Pre-erupción (14 días):**
   - Incremento exponencial de actividad
   - Aceleración positiva detectada
   - Riesgo alto/crítico

### Validación de Endpoints

- ✅ `GET /health` - Verificación de salud
- ✅ `GET /data/generate` - Generación de datos
- ✅ `POST /calculate/derivatives` - Cálculo de derivadas
- ✅ `POST /predict` - Predicción de erupciones

## Mejoras Futuras

### Funcionalidades Pendientes
1. **Datos Reales:** Integración con datos sísmicos reales en lugar de sintéticos
2. **Alertas en Tiempo Real:** WebSocket para notificaciones instantáneas
3. **Histórico de Análisis:** Almacenamiento de análisis previos
4. **Exportación de Reportes:** PDF/Excel con análisis detallados
5. **Configuración Avanzada:** Parámetros de detección personalizables

### Optimizaciones Técnicas
1. **Caching:** Implementar cache para datos frecuentemente consultados
2. **Lazy Loading:** Cargar componentes bajo demanda
3. **Error Boundaries:** Manejo más robusto de errores
4. **Performance:** Optimización de gráficos para datasets grandes

## Conclusión

La integración entre el monolito Python de cálculo volcánico y el frontend React ha sido implementada exitosamente. La solución proporciona:

- **Visualización interactiva** de análisis de derivadas sísmicas
- **Detección automática** de aceleración volcánica
- **Predicción de erupciones** basada en modelos científicos
- **Interfaz intuitiva** para operadores de monitoreo volcánico

La arquitectura modular permite fácil extensión y mantenimiento, mientras que el uso de tecnologías modernas (React, TypeScript, Recharts) asegura un rendimiento óptimo y una experiencia de usuario superior.

---

**Documentación generada:** 11 de enero de 2025  
**Versión:** 1.0  
**Autor:** Claude Code  
**Estado:** ✅ Integración completada y funcional