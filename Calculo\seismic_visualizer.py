#!/usr/bin/env python3
"""
Sistema de Visualización de Datos Sísmicos
Genera gráficos interactivos en HTML/CSS/JavaScript
"""

import json
import os
from datetime import datetime, timedelta
from typing import List, Dict, Any
from pathlib import Path

from core.derivatives import DerivativeCalculator
from core.ffm_model import FFMModel
from core.data_generator import SeismicDataGenerator
from core.alert_system import AlertSystem
from models.volcanic_data import SeismicReading, AlertLevel


class SeismicVisualizer:
    """Genera visualizaciones interactivas de datos sísmicos"""
    
    def __init__(self, output_dir: str = "visualizations"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
    def generate_html_template(self) -> str:
        """Genera la plantilla HTML base con estilos CSS"""
        return """
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌋 Visualizador de Datos Sísmicos</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: #ffffff;
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 3em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }
        
        .header .subtitle {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.15);
            padding: 20px;
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(5px);
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-card h3 {
            margin-bottom: 10px;
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .stat-value {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 0.9em;
            opacity: 0.8;
        }
        
        .chart-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }
        
        .chart-title {
            color: #2c3e50;
            font-size: 1.8em;
            margin-bottom: 20px;
            text-align: center;
            font-weight: bold;
        }
        
        .chart {
            width: 100%;
            height: 400px;
            position: relative;
        }
        
        .alert-panel {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            border-left: 5px solid #ff6b6b;
        }
        
        .alert-level {
            display: inline-block;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: bold;
            font-size: 1.2em;
            margin-bottom: 10px;
        }
        
        .alert-red {
            background: #ff6b6b;
            color: white;
        }
        
        .alert-orange {
            background: #ffa500;
            color: white;
        }
        
        .alert-yellow {
            background: #ffeb3b;
            color: #333;
        }
        
        .alert-green {
            background: #4caf50;
            color: white;
        }
        
        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .control-btn {
            padding: 10px 20px;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 25px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .control-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        .control-btn.active {
            background: #4caf50;
        }
        
        .tooltip {
            position: absolute;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 0.9em;
            pointer-events: none;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.3);
            opacity: 0.8;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .controls {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌋 Visualizador de Datos Sísmicos</h1>
            <p class="subtitle">Sistema de Predicción Volcánica - Análisis en Tiempo Real</p>
        </div>
        
        <div class="stats-grid">
            <div class="stat-card">
                <h3>📊 Eventos Totales</h3>
                <div class="stat-value" id="total-events">--</div>
                <div class="stat-label">Últimas 24 horas</div>
            </div>
            
            <div class="stat-card">
                <h3>📈 Magnitud Máxima</h3>
                <div class="stat-value" id="max-magnitude">--</div>
                <div class="stat-label">Escala de Richter</div>
            </div>
            
            <div class="stat-card">
                <h3>🎯 Probabilidad de Erupción</h3>
                <div class="stat-value" id="eruption-probability">--</div>
                <div class="stat-label">Análisis FFM</div>
            </div>
            
            <div class="stat-card">
                <h3>⚡ Aceleración Máxima</h3>
                <div class="stat-value" id="max-acceleration">--</div>
                <div class="stat-label">Derivada Segunda</div>
            </div>
        </div>
        
        <div class="alert-panel">
            <div class="alert-level" id="alert-level">VERDE</div>
            <div>
                <strong>Estado del Sistema:</strong> <span id="alert-message">Monitoreo normal</span>
            </div>
        </div>
        
        <div class="controls">
            <button class="control-btn active" onclick="showChart('magnitude', event)">📊 Magnitud vs Tiempo</button>
            <button class="control-btn" onclick="showChart('derivatives', event)">📈 Derivadas</button>
            <button class="control-btn" onclick="showChart('frequency', event)">📋 Distribución de Frecuencia</button>
            <button class="control-btn" onclick="showChart('patterns', event)">🔍 Patrones</button>
            <button class="control-btn" onclick="toggleRealTime(event)">⏱️ Tiempo Real</button>
        </div>
        
        <div class="chart-container">
            <div class="chart-title" id="chart-title">Magnitud Sísmica vs Tiempo</div>
            <div class="chart" id="main-chart"></div>
        </div>
        
        <div class="chart-container">
            <div class="chart-title">Análisis de Aceleración (Segunda Derivada)</div>
            <div class="chart" id="acceleration-chart"></div>
        </div>
        
        <div class="chart-container">
            <div class="chart-title">Patrón de Actividad en Tiempo Real</div>
            <div class="chart" id="realtime-chart"></div>
        </div>
        
        <div class="tooltip" id="tooltip"></div>
        
        <div class="footer">
            <p>Sistema desarrollado para análisis y predicción de actividad volcánica</p>
            <p>Datos actualizados: <span id="last-update">--</span></p>
        </div>
    </div>

    <script src="https://d3js.org/d3.v7.min.js"></script>
    <script>
        // Datos sísmicos (serán reemplazados por datos reales)
        let seismicData = DATA_PLACEHOLDER || [];
        let derivativeData = DERIVATIVE_DATA_PLACEHOLDER || [];
        let ffmData = FFM_DATA_PLACEHOLDER || {};
        let alertData = ALERT_DATA_PLACEHOLDER || {};
        
        // Variables globales
        let currentChart = 'magnitude';
        let isRealTimeActive = false;
        let realTimeInterval = null;
        
        // Funciones principales
        function updateStats() {
            if (!seismicData || seismicData.length === 0) {
                document.getElementById('total-events').textContent = '0';
                document.getElementById('max-magnitude').textContent = '0.0';
                document.getElementById('eruption-probability').textContent = '0.0%';
                document.getElementById('max-acceleration').textContent = '0.0';
                return;
            }
            
            const totalEvents = seismicData.length;
            const maxMagnitude = Math.max(...seismicData.map(d => d.magnitude || 0));
            const maxAcceleration = derivativeData && derivativeData.length > 0 ? 
                Math.max(...derivativeData.map(d => Math.abs(d.second_derivative || 0))) : 0;
            const eruptionProbability = ffmData.failure_probability || 0;
            
            document.getElementById('total-events').textContent = totalEvents;
            document.getElementById('max-magnitude').textContent = maxMagnitude.toFixed(2);
            document.getElementById('eruption-probability').textContent = (eruptionProbability * 100).toFixed(1) + '%';
            document.getElementById('max-acceleration').textContent = maxAcceleration.toFixed(2);
        }
        
        function updateAlertStatus() {
            const alertLevel = alertData.level || 'GREEN';
            const alertMessage = alertData.message || 'Sistema funcionando normalmente';
            
            const levelElement = document.getElementById('alert-level');
            levelElement.textContent = alertLevel;
            levelElement.className = `alert-level alert-${alertLevel.toLowerCase()}`;
            
            document.getElementById('alert-message').textContent = alertMessage;
        }
        
        function updateLastUpdate() {
            document.getElementById('last-update').textContent = new Date().toLocaleString();
        }
        
        // Inicializar visualización
        document.addEventListener('DOMContentLoaded', function() {
            updateStats();
            updateAlertStatus();
            showChart('magnitude');
            updateLastUpdate();
        });
        
        function showChart(chartType, event) {
            // Actualizar botones
            document.querySelectorAll('.control-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // Solo agregar clase active si hay un evento (llamada desde botón)
            if (event && event.target) {
                event.target.classList.add('active');
            } else {
                // Si no hay evento, encontrar el botón correspondiente
                const buttons = document.querySelectorAll('.control-btn');
                buttons.forEach(btn => {
                    if (btn.textContent.includes('Magnitud') && chartType === 'magnitude') {
                        btn.classList.add('active');
                    }
                });
            }
            
            currentChart = chartType;
            
            try {
                switch(chartType) {
                    case 'magnitude':
                        drawMagnitudeChart();
                        break;
                    case 'derivatives':
                        drawDerivativeChart();
                        break;
                    case 'frequency':
                        drawFrequencyChart();
                        break;
                    case 'patterns':
                        drawPatternChart();
                        break;
                    default:
                        console.warn('Tipo de gráfico no reconocido:', chartType);
                        drawMagnitudeChart();
                }
            } catch (error) {
                console.error('Error al dibujar gráfico:', error);
                document.getElementById('chart-title').textContent = 'Error al cargar gráfico';
            }
        }
        
        function drawMagnitudeChart() {
            const container = d3.select('#main-chart');
            container.selectAll('*').remove();
            
            // Validar datos
            if (!seismicData || seismicData.length === 0) {
                container.append('div')
                    .style('text-align', 'center')
                    .style('padding', '50px')
                    .style('color', '#666')
                    .html('📊 No hay datos sísmicos disponibles');
                document.getElementById('chart-title').textContent = 'Datos No Disponibles';
                return;
            }
            
            const margin = {top: 20, right: 30, bottom: 40, left: 50};
            const width = container.node().getBoundingClientRect().width - margin.left - margin.right;
            const height = 400 - margin.top - margin.bottom;
            
            if (width <= 0 || height <= 0) {
                console.warn('Dimensiones inválidas para el gráfico');
                return;
            }
            
            const svg = container.append('svg')
                .attr('width', width + margin.left + margin.right)
                .attr('height', height + margin.top + margin.bottom);
            
            const g = svg.append('g')
                .attr('transform', `translate(${margin.left},${margin.top})`);
            
            // Escalas
            const xScale = d3.scaleTime()
                .domain(d3.extent(seismicData, d => new Date(d.timestamp)))
                .range([0, width]);
            
            const yScale = d3.scaleLinear()
                .domain(d3.extent(seismicData, d => d.magnitude))
                .range([height, 0]);
            
            // Línea
            const line = d3.line()
                .x(d => xScale(new Date(d.timestamp)))
                .y(d => yScale(d.magnitude))
                .curve(d3.curveMonotoneX);
            
            // Dibujar ejes
            g.append('g')
                .attr('transform', `translate(0,${height})`)
                .call(d3.axisBottom(xScale).tickFormat(d3.timeFormat('%H:%M')));
            
            g.append('g')
                .call(d3.axisLeft(yScale));
            
            // Dibujar línea
            g.append('path')
                .datum(seismicData)
                .attr('fill', 'none')
                .attr('stroke', '#ff6b6b')
                .attr('stroke-width', 2)
                .attr('d', line);
            
            // Puntos
            g.selectAll('.dot')
                .data(seismicData)
                .enter().append('circle')
                .attr('class', 'dot')
                .attr('cx', d => xScale(new Date(d.timestamp)))
                .attr('cy', d => yScale(d.magnitude))
                .attr('r', 4)
                .attr('fill', d => d.magnitude > 4 ? '#ff4444' : '#4CAF50')
                .on('mouseover', function(event, d) {
                    showTooltip(event, `Magnitud: ${d.magnitude.toFixed(2)}<br>Tiempo: ${new Date(d.timestamp).toLocaleString()}`);
                })
                .on('mouseout', hideTooltip);
            
            // Etiquetas de ejes
            g.append('text')
                .attr('transform', 'rotate(-90)')
                .attr('y', 0 - margin.left)
                .attr('x', 0 - (height / 2))
                .attr('dy', '1em')
                .style('text-anchor', 'middle')
                .style('fill', '#2c3e50')
                .text('Magnitud (Richter)');
            
            g.append('text')
                .attr('transform', `translate(${width / 2}, ${height + margin.bottom})`)
                .style('text-anchor', 'middle')
                .style('fill', '#2c3e50')
                .text('Tiempo');
            
            document.getElementById('chart-title').textContent = 'Magnitud Sísmica vs Tiempo';
        }
        
        function drawDerivativeChart() {
            const container = d3.select('#main-chart');
            container.selectAll('*').remove();
            
            // Validar datos
            if (!derivativeData || derivativeData.length === 0) {
                container.append('div')
                    .style('text-align', 'center')
                    .style('padding', '50px')
                    .style('color', '#666')
                    .html('📈 No hay datos de derivadas disponibles');
                document.getElementById('chart-title').textContent = 'Datos de Derivadas No Disponibles';
                return;
            }
            
            const margin = {top: 20, right: 30, bottom: 40, left: 50};
            const width = container.node().getBoundingClientRect().width - margin.left - margin.right;
            const height = 400 - margin.top - margin.bottom;
            
            if (width <= 0 || height <= 0) {
                console.warn('Dimensiones inválidas para el gráfico de derivadas');
                return;
            }
            
            const svg = container.append('svg')
                .attr('width', width + margin.left + margin.right)
                .attr('height', height + margin.top + margin.bottom);
            
            const g = svg.append('g')
                .attr('transform', `translate(${margin.left},${margin.top})`);
            
            // Escalas
            const xScale = d3.scaleLinear()
                .domain([0, derivativeData.length - 1])
                .range([0, width]);
            
            const yScale = d3.scaleLinear()
                .domain(d3.extent(derivativeData, d => d.first_derivative || 0))
                .range([height, 0]);
            
            // Línea para primera derivada
            const line = d3.line()
                .x((d, i) => xScale(i))
                .y(d => yScale(d.first_derivative || 0))
                .curve(d3.curveMonotoneX);
            
            // Dibujar ejes
            g.append('g')
                .attr('transform', `translate(0,${height})`)
                .call(d3.axisBottom(xScale));
            
            g.append('g')
                .call(d3.axisLeft(yScale));
            
            // Línea de referencia en y=0
            g.append('line')
                .attr('x1', 0)
                .attr('x2', width)
                .attr('y1', yScale(0))
                .attr('y2', yScale(0))
                .attr('stroke', '#666')
                .attr('stroke-dasharray', '3,3');
            
            // Dibujar línea
            g.append('path')
                .datum(derivativeData)
                .attr('fill', 'none')
                .attr('stroke', '#2196F3')
                .attr('stroke-width', 2)
                .attr('d', line);
            
            // Puntos críticos
            g.selectAll('.critical-point')
                .data(derivativeData.filter(d => Math.abs(d.first_derivative || 0) > 0.5))
                .enter().append('circle')
                .attr('class', 'critical-point')
                .attr('cx', (d, i) => xScale(derivativeData.indexOf(d)))
                .attr('cy', d => yScale(d.first_derivative || 0))
                .attr('r', 6)
                .attr('fill', '#ff6b6b')
                .attr('stroke', '#fff')
                .attr('stroke-width', 2);
            
            document.getElementById('chart-title').textContent = 'Análisis de Derivadas - Velocidad de Cambio';
        }
        
        function drawFrequencyChart() {
            const container = d3.select('#main-chart');
            container.selectAll('*').remove();
            
            // Validar datos
            if (!seismicData || seismicData.length === 0) {
                container.append('div')
                    .style('text-align', 'center')
                    .style('padding', '50px')
                    .style('color', '#666')
                    .html('📋 No hay datos para histograma');
                document.getElementById('chart-title').textContent = 'Datos No Disponibles';
                return;
            }
            
            // Crear histograma de magnitudes
            const magnitudes = seismicData.map(d => d.magnitude || 0);
            const bins = d3.histogram()
                .domain(d3.extent(magnitudes))
                .thresholds(10)(magnitudes);
            
            const margin = {top: 20, right: 30, bottom: 40, left: 50};
            const width = container.node().getBoundingClientRect().width - margin.left - margin.right;
            const height = 400 - margin.top - margin.bottom;
            
            const svg = container.append('svg')
                .attr('width', width + margin.left + margin.right)
                .attr('height', height + margin.top + margin.bottom);
            
            const g = svg.append('g')
                .attr('transform', `translate(${margin.left},${margin.top})`);
            
            const xScale = d3.scaleLinear()
                .domain(d3.extent(magnitudes))
                .range([0, width]);
            
            const yScale = d3.scaleLinear()
                .domain([0, d3.max(bins, d => d.length)])
                .range([height, 0]);
            
            // Dibujar ejes
            g.append('g')
                .attr('transform', `translate(0,${height})`)
                .call(d3.axisBottom(xScale));
            
            g.append('g')
                .call(d3.axisLeft(yScale));
            
            // Barras
            g.selectAll('.bar')
                .data(bins)
                .enter().append('rect')
                .attr('class', 'bar')
                .attr('x', d => xScale(d.x0))
                .attr('y', d => yScale(d.length))
                .attr('width', d => xScale(d.x1) - xScale(d.x0) - 1)
                .attr('height', d => height - yScale(d.length))
                .attr('fill', '#4CAF50')
                .attr('opacity', 0.7);
            
            document.getElementById('chart-title').textContent = 'Distribución de Frecuencia de Magnitudes';
        }
        
        function drawPatternChart() {
            // Implementar gráfico de patrones
            document.getElementById('chart-title').textContent = 'Análisis de Patrones Sísmicos';
        }
        
        function toggleRealTime(event) {
            isRealTimeActive = !isRealTimeActive;
            const btn = event ? event.target : document.querySelector('.control-btn:last-child');
            
            if (isRealTimeActive) {
                btn.textContent = '⏹️ Detener Tiempo Real';
                btn.classList.add('active');
                startRealTimeSimulation();
            } else {
                btn.textContent = '⏱️ Tiempo Real';
                btn.classList.remove('active');
                stopRealTimeSimulation();
            }
        }
        
        function startRealTimeSimulation() {
            realTimeInterval = setInterval(() => {
                // Simular nuevos datos
                const newMagnitude = Math.random() * 3 + 1;
                const newTimestamp = new Date().toISOString();
                
                seismicData.push({
                    timestamp: newTimestamp,
                    magnitude: newMagnitude,
                    depth: Math.random() * 10 + 5
                });
                
                // Mantener solo los últimos 100 puntos
                if (seismicData.length > 100) {
                    seismicData.shift();
                }
                
                updateStats();
                if (currentChart === 'magnitude') {
                    drawMagnitudeChart();
                }
                
                updateLastUpdate();
            }, 2000);
        }
        
        function stopRealTimeSimulation() {
            if (realTimeInterval) {
                clearInterval(realTimeInterval);
                realTimeInterval = null;
            }
        }
        
        function showTooltip(event, html) {
            const tooltip = document.getElementById('tooltip');
            tooltip.innerHTML = html;
            tooltip.style.left = (event.pageX + 10) + 'px';
            tooltip.style.top = (event.pageY - 10) + 'px';
            tooltip.style.opacity = '1';
        }
        
        function hideTooltip() {
            document.getElementById('tooltip').style.opacity = '0';
        }
        
        function updateLastUpdate() {
            document.getElementById('last-update').textContent = new Date().toLocaleString();
        }
        
        // Redimensionar gráficos cuando cambie el tamaño de la ventana
        window.addEventListener('resize', function() {
            if (currentChart) {
                showChart(currentChart);
            }
        });
    </script>
</body>
</html>
        """
    
    def prepare_data_for_visualization(self) -> Dict[str, Any]:
        """Prepara los datos para visualización"""
        # Generar datos sintéticos
        generator = SeismicDataGenerator(seed=42)
        calculator = DerivativeCalculator()
        ffm_model = FFMModel()
        alert_system = AlertSystem()
        
        # Datos sísmicos
        seismic_data = generator.generate_seismic_sequence(
            days=2, 
            eruption_scenario=True
        )
        
        # Cálculo de derivadas
        derivative_result = calculator.calculate_derivatives(seismic_data)
        
        # Análisis FFM
        ffm_result = ffm_model.analyze_failure_pattern(seismic_data, derivative_result)
        
        # Sistema de alertas
        alert_level = alert_system.evaluate_risk(ffm_result)
        active_alerts = alert_system.get_active_alerts()
        
        # Preparar datos para JavaScript
        seismic_json = []
        for reading in seismic_data:
            seismic_json.append({
                'timestamp': reading.timestamp.isoformat(),
                'magnitude': reading.magnitude,
                'depth': reading.depth,
                'frequency': reading.frequency,
                'duration': reading.duration
            })
        
        derivative_json = []
        for i, point in enumerate(derivative_result.first_derivative):
            derivative_json.append({
                'index': i,
                'first_derivative': point,
                'second_derivative': derivative_result.second_derivative[i] if i < len(derivative_result.second_derivative) else 0
            })
        
        ffm_json = {
            'failure_probability': ffm_result.failure_probability,
            'time_to_failure': ffm_result.time_to_failure,
            'confidence_level': ffm_result.confidence_level,
            'acceleration_trend': ffm_result.acceleration_trend,
            'precursor_indicators': ffm_result.precursor_indicators
        }
        
        alert_json = {
            'level': alert_level.value,
            'message': active_alerts[0].message if active_alerts else 'Sistema funcionando normalmente',
            'probability': active_alerts[0].probability if active_alerts else 0.0,
            'timestamp': active_alerts[0].timestamp.isoformat() if active_alerts else datetime.now().isoformat()
        }
        
        return {
            'seismic_data': seismic_json,
            'derivative_data': derivative_json,
            'ffm_data': ffm_json,
            'alert_data': alert_json
        }
    
    def generate_visualization(self, filename: str = "seismic_dashboard.html") -> str:
        """Genera el archivo HTML con la visualización completa"""
        
        # Preparar datos
        data = self.prepare_data_for_visualization()
        
        # Generar HTML
        html_content = self.generate_html_template()
        
        # Reemplazar placeholders con datos reales (orden importante - más específicos primero)
        placeholders = {
            'DERIVATIVE_DATA_PLACEHOLDER': json.dumps(data['derivative_data'], indent=2),
            'FFM_DATA_PLACEHOLDER': json.dumps(data['ffm_data'], indent=2),
            'ALERT_DATA_PLACEHOLDER': json.dumps(data['alert_data'], indent=2),
            'DATA_PLACEHOLDER': json.dumps(data['seismic_data'], indent=2)
        }
        
        # Debug: Mostrar información de los datos
        print(f"   📊 Datos sísmicos: {len(data['seismic_data'])} eventos")
        print(f"   📈 Datos derivadas: {len(data['derivative_data'])} puntos")
        print(f"   🎯 Datos FFM: {len(data['ffm_data'])} campos")
        print(f"   🚨 Datos alertas: {len(data['alert_data'])} campos")
        
        # Reemplazar en orden específico para evitar conflictos
        for placeholder, replacement in placeholders.items():
            if placeholder in html_content:
                count_before = html_content.count(placeholder)
                html_content = html_content.replace(placeholder, replacement)
                count_after = html_content.count(placeholder)
                print(f"   ✅ {placeholder}: Reemplazado {count_before - count_after} veces")
            else:
                print(f"   ⚠️  {placeholder}: No encontrado en template")
        
        # Guardar archivo
        output_path = self.output_dir / filename
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        return str(output_path)
    
    def generate_multiple_visualizations(self):
        """Genera múltiples visualizaciones para diferentes escenarios"""
        scenarios = [
            ('normal', 'dashboard_normal.html', False),
            ('pre_eruption', 'dashboard_pre_eruption.html', True),
            ('critical', 'dashboard_critical.html', True)
        ]
        
        generated_files = []
        
        for scenario_name, filename, is_eruption in scenarios:
            print(f"🎨 Generando visualización para escenario: {scenario_name}")
            
            # Generar datos específicos para el escenario
            generator = SeismicDataGenerator(seed=42 if scenario_name == 'normal' else 123)
            
            if scenario_name == 'critical':
                # Datos más intensos para escenario crítico
                seismic_data = generator.generate_seismic_sequence(
                    days=1, 
                    eruption_scenario=True
                )
                # Agregar eventos más intensos
                for i in range(10):
                    seismic_data.append(
                        generator.generate_real_time_data(
                            'critical', 
                            datetime.now() + timedelta(minutes=i*10)
                        )
                    )
            else:
                seismic_data = generator.generate_seismic_sequence(
                    days=2, 
                    eruption_scenario=is_eruption
                )
            
            # Procesar datos
            calculator = DerivativeCalculator()
            derivative_result = calculator.calculate_derivatives(seismic_data)
            
            ffm_model = FFMModel()
            ffm_result = ffm_model.analyze_failure_pattern(seismic_data, derivative_result)
            
            alert_system = AlertSystem()
            alert_level = alert_system.evaluate_risk(ffm_result)
            active_alerts = alert_system.get_active_alerts()
            
            # Preparar datos específicos
            seismic_json = []
            for reading in seismic_data:
                seismic_json.append({
                    'timestamp': reading.timestamp.isoformat(),
                    'magnitude': reading.magnitude,
                    'depth': reading.depth,
                    'frequency': reading.frequency,
                    'duration': reading.duration
                })
            
            derivative_json = []
            for i, point in enumerate(derivative_result.first_derivative):
                derivative_json.append({
                    'index': i,
                    'first_derivative': point,
                    'second_derivative': derivative_result.second_derivative[i] if i < len(derivative_result.second_derivative) else 0
                })
            
            ffm_json = {
                'failure_probability': ffm_result.failure_probability,
                'time_to_failure': ffm_result.time_to_failure,
                'confidence_level': ffm_result.confidence_level,
                'acceleration_trend': ffm_result.acceleration_trend,
                'precursor_indicators': ffm_result.precursor_indicators
            }
            
            alert_json = {
                'level': alert_level.value,
                'message': active_alerts[0].message if active_alerts else f'Escenario {scenario_name}',
                'probability': active_alerts[0].probability if active_alerts else 0.0,
                'timestamp': active_alerts[0].timestamp.isoformat() if active_alerts else datetime.now().isoformat()
            }
            
            # Generar HTML
            html_content = self.generate_html_template()
            
            # Reemplazar placeholders con validación (orden importante)
            placeholders = {
                'DERIVATIVE_DATA_PLACEHOLDER': json.dumps(derivative_json, indent=2),
                'FFM_DATA_PLACEHOLDER': json.dumps(ffm_json, indent=2),
                'ALERT_DATA_PLACEHOLDER': json.dumps(alert_json, indent=2),
                'DATA_PLACEHOLDER': json.dumps(seismic_json, indent=2)
            }
            
            for placeholder, replacement in placeholders.items():
                if placeholder in html_content:
                    html_content = html_content.replace(placeholder, replacement)
            
            # Guardar archivo
            output_path = self.output_dir / filename
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            generated_files.append(str(output_path))
            print(f"   ✅ Archivo generado: {output_path}")
        
        return generated_files


def main():
    """Función principal para generar visualizaciones"""
    print("🎨 GENERADOR DE VISUALIZACIONES SÍSMICAS")
    print("   Creando dashboards interactivos HTML/CSS/JavaScript")
    print("   Basado en datos reales del sistema de predicción volcánica")
    
    try:
        # Crear visualizador
        visualizer = SeismicVisualizer()
        
        # Generar visualización principal
        print("\n📊 Generando dashboard principal...")
        main_dashboard = visualizer.generate_visualization()
        print(f"   ✅ Dashboard principal creado: {main_dashboard}")
        
        # Generar múltiples escenarios
        print("\n🎯 Generando dashboards para diferentes escenarios...")
        scenario_dashboards = visualizer.generate_multiple_visualizations()
        
        print("\n🚀 VISUALIZACIONES COMPLETADAS")
        print(f"   Archivos generados:")
        print(f"   • Dashboard principal: {main_dashboard}")
        for dashboard in scenario_dashboards:
            print(f"   • {dashboard}")
        
        print(f"\n💡 Para ver las visualizaciones:")
        print(f"   1. Abra cualquier archivo HTML en su navegador")
        print(f"   2. Use los controles para cambiar entre gráficos")
        print(f"   3. Active el modo tiempo real para simulación")
        
        # Abrir automáticamente el dashboard principal
        import webbrowser
        webbrowser.open(f"file://{os.path.abspath(main_dashboard)}")
        
    except Exception as e:
        print(f"\n❌ Error al generar visualizaciones: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
