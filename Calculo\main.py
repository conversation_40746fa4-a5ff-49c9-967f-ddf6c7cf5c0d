"""
Volcano Prediction System - Main Application
Sistema de Predicción Volcánica utilizando Derivadas y FFM
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
import uvicorn
import logging
from datetime import datetime

from core.derivatives import DerivativeCalculator
from core.ffm_model import FFMModel
from core.data_generator import SeismicDataGenerator
from core.alert_system import AlertSystem
from models.volcanic_data import SeismicReading, PredictionResult, AlertLevel

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Inicializar FastAPI
app = FastAPI(
    title="Volcano Prediction System",
    description="Sistema de Predicción Volcánica mediante Derivadas y FFM",
    version="1.0.0"
)

# Configurar CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Inicializar componentes del sistema
derivative_calc = DerivativeCalculator()
ffm_model = FFMModel()
data_generator = SeismicDataGenerator()
alert_system = AlertSystem()

# Modelos de request/response
class PredictionRequest(BaseModel):
    seismic_data: List[SeismicReading]
    time_window: int = 24  # horas
    calculation_detail: bool = True

class DerivativeRequest(BaseModel):
    data_points: List[float]
    time_points: List[float]
    show_steps: bool = True

@app.get("/")
async def root():
    """Endpoint raíz con información del sistema"""
    return {
        "message": "Sistema de Predicción Volcánica",
        "version": "1.0.0",
        "status": "active",
        "timestamp": datetime.utcnow()
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "timestamp": datetime.utcnow()}

@app.post("/predict")
async def predict_eruption(request: PredictionRequest) -> PredictionResult:
    """
    Predice la probabilidad de erupción volcánica
    usando derivadas y el modelo FFM
    """
    try:
        logger.info(f"Procesando predicción para {len(request.seismic_data)} lecturas sísmicas")
        
        # Calcular derivadas de la actividad sísmica
        derivative_results = derivative_calc.calculate_derivatives(
            request.seismic_data,
            show_steps=request.calculation_detail
        )
        
        # Aplicar el modelo FFM
        ffm_results = ffm_model.analyze_failure_pattern(
            request.seismic_data,
            derivative_results
        )
        
        # Generar alerta si es necesario
        alert_level = alert_system.evaluate_risk(ffm_results)
        
        # Construir resultado
        result = PredictionResult(
            timestamp=datetime.utcnow(),
            eruption_probability=ffm_results.failure_probability,
            alert_level=alert_level,
            derivative_analysis=derivative_results,
            ffm_analysis=ffm_results,
            calculation_steps=derivative_results.calculation_steps if request.calculation_detail else None
        )
        
        logger.info(f"Predicción completada: {alert_level.value} - {ffm_results.failure_probability:.2%}")
        return result
        
    except Exception as e:
        logger.error(f"Error en predicción: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error en predicción: {str(e)}")

@app.post("/calculate/derivatives")
async def calculate_derivatives(request: DerivativeRequest) -> Dict[str, Any]:
    """
    Calcula derivadas primera y segunda paso a paso
    """
    try:
        if len(request.data_points) != len(request.time_points):
            raise HTTPException(
                status_code=400, 
                detail="Los puntos de datos y tiempo deben tener la misma longitud"
            )
        
        # Crear datos sísmicos temporales
        seismic_data = [
            SeismicReading(
                timestamp=datetime.utcnow(),
                magnitude=mag,
                frequency=1.0,
                duration=1.0,
                depth=5.0
            )
            for mag in request.data_points
        ]
        
        # Calcular derivadas
        results = derivative_calc.calculate_derivatives(
            seismic_data,
            show_steps=request.show_steps
        )
        
        return {
            "first_derivative": results.first_derivative,
            "second_derivative": results.second_derivative,
            "acceleration_points": results.acceleration_points,
            "calculation_steps": results.calculation_steps if request.show_steps else None,
            "timestamp": datetime.utcnow()
        }
        
    except Exception as e:
        logger.error(f"Error en cálculo de derivadas: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error en cálculo: {str(e)}")

@app.get("/data/generate")
async def generate_sample_data(
    days: int = 7,
    eruption_scenario: bool = False
) -> Dict[str, Any]:
    """
    Genera datos sísmicos ficticios para pruebas
    """
    try:
        data = data_generator.generate_seismic_sequence(
            days=days,
            eruption_scenario=eruption_scenario
        )
        
        return {
            "data": data,
            "count": len(data),
            "period_days": days,
            "eruption_scenario": eruption_scenario,
            "generated_at": datetime.utcnow()
        }
        
    except Exception as e:
        logger.error(f"Error generando datos: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error generando datos: {str(e)}")

@app.post("/analyze/ffm")
async def analyze_ffm(request: PredictionRequest) -> Dict[str, Any]:
    """
    Analiza usando solo el modelo FFM
    """
    try:
        # Calcular derivadas primero
        derivative_results = derivative_calc.calculate_derivatives(request.seismic_data)
        
        # Aplicar FFM
        ffm_results = ffm_model.analyze_failure_pattern(
            request.seismic_data,
            derivative_results
        )
        
        return {
            "ffm_analysis": ffm_results,
            "failure_probability": ffm_results.failure_probability,
            "time_to_failure": ffm_results.time_to_failure,
            "acceleration_trend": ffm_results.acceleration_trend,
            "confidence_level": ffm_results.confidence_level,
            "timestamp": datetime.utcnow()
        }
        
    except Exception as e:
        logger.error(f"Error en análisis FFM: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error en análisis FFM: {str(e)}")

@app.get("/alerts/current")
async def get_current_alerts() -> Dict[str, Any]:
    """
    Obtiene las alertas actuales del sistema
    """
    try:
        alerts = alert_system.get_active_alerts()
        return {
            "active_alerts": alerts,
            "count": len(alerts),
            "timestamp": datetime.utcnow()
        }
        
    except Exception as e:
        logger.error(f"Error obteniendo alertas: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error obteniendo alertas: {str(e)}")

@app.get("/system/status")
async def get_system_status() -> Dict[str, Any]:
    """
    Estado del sistema de cálculo
    """
    return {
        "system_status": "operational",
        "components": {
            "derivative_calculator": "active",
            "ffm_model": "active",
            "data_generator": "active",
            "alert_system": "active"
        },
        "last_calculation": datetime.utcnow(),
        "version": "1.0.0"
    }

if __name__ == "__main__":
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True)
