#!/usr/bin/env python3
"""
Prueba de integración del API para verificar que las correcciones funcionan
"""

import requests
import json
import sys

def test_api_integration():
    """Prueba la integración completa del API"""
    base_url = "http://localhost:8000"
    
    print("🧪 PRUEBA DE INTEGRACIÓN API")
    print("=" * 50)
    
    try:
        # 1. Verificar salud del servicio
        print("1. Verificando salud del servicio...")
        health_response = requests.get(f"{base_url}/health", timeout=5)
        if health_response.status_code == 200:
            print("   ✅ Servicio saludable")
        else:
            print("   ❌ Servicio no disponible")
            return False
        
        # 2. Generar datos normales
        print("2. Generando datos normales...")
        data_response = requests.get(
            f"{base_url}/data/generate",
            params={"days": 3, "eruption_scenario": False},
            timeout=10
        )
        
        if data_response.status_code != 200:
            print(f"   ❌ Error generando datos: {data_response.status_code}")
            return False
        
        data = data_response.json()
        print(f"   ✅ Generados {data['count']} eventos")
        
        # 3. Calcular derivadas con datos normales
        print("3. Calculando derivadas para datos normales...")
        prediction_payload = {
            "seismic_data": data["data"],
            "time_window": 24,
            "calculation_detail": True
        }
        
        prediction_response = requests.post(
            f"{base_url}/predict",
            json=prediction_payload,
            timeout=15
        )
        
        if prediction_response.status_code != 200:
            print(f"   ❌ Error en predicción: {prediction_response.status_code}")
            print(f"   Respuesta: {prediction_response.text}")
            return False
        
        prediction_result = prediction_response.json()
        print(f"   ✅ Predicción completada")
        print(f"   Max aceleración: {prediction_result['derivative_analysis']['max_acceleration']:.6f}")
        print(f"   Tendencia: {prediction_result['derivative_analysis']['acceleration_trend']}")
        
        # Verificar que actividad normal se clasifique correctamente
        if prediction_result['derivative_analysis']['acceleration_trend'] == "ACTIVIDAD ESTABLE":
            print("   ✅ Actividad normal clasificada correctamente")
        else:
            print(f"   ❌ Actividad normal mal clasificada: {prediction_result['derivative_analysis']['acceleration_trend']}")
            return False
        
        # 4. Generar datos pre-erupción
        print("4. Generando datos pre-erupción...")
        eruption_data_response = requests.get(
            f"{base_url}/data/generate",
            params={"days": 3, "eruption_scenario": True},
            timeout=10
        )
        
        if eruption_data_response.status_code != 200:
            print(f"   ❌ Error generando datos pre-erupción: {eruption_data_response.status_code}")
            return False
        
        eruption_data = eruption_data_response.json()
        print(f"   ✅ Generados {eruption_data['count']} eventos pre-erupción")
        
        # 5. Calcular derivadas con datos pre-erupción
        print("5. Calculando derivadas para datos pre-erupción...")
        eruption_payload = {
            "seismic_data": eruption_data["data"],
            "time_window": 24,
            "calculation_detail": True
        }
        
        eruption_response = requests.post(
            f"{base_url}/predict",
            json=eruption_payload,
            timeout=15
        )
        
        if eruption_response.status_code != 200:
            print(f"   ❌ Error en predicción pre-erupción: {eruption_response.status_code}")
            return False
        
        eruption_result = eruption_response.json()
        print(f"   ✅ Predicción pre-erupción completada")
        print(f"   Max aceleración: {eruption_result['derivative_analysis']['max_acceleration']:.6f}")
        print(f"   Tendencia: {eruption_result['derivative_analysis']['acceleration_trend']}")
        
        # Verificar que actividad pre-erupción se detecte
        if "CRÍTICA" in eruption_result['derivative_analysis']['acceleration_trend'] or \
           "MODERADA" in eruption_result['derivative_analysis']['acceleration_trend']:
            print("   ✅ Actividad pre-erupción detectada correctamente")
        else:
            print(f"   ❌ Actividad pre-erupción no detectada: {eruption_result['derivative_analysis']['acceleration_trend']}")
            return False
        
        # 6. Probar endpoint de derivadas directamente
        print("6. Probando endpoint de derivadas directamente...")
        derivative_payload = {
            "data_points": [2.0, 2.1, 2.2, 2.3, 2.4, 2.5],
            "time_points": [0, 1, 2, 3, 4, 5],
            "show_steps": True
        }
        
        derivative_response = requests.post(
            f"{base_url}/calculate/derivatives",
            json=derivative_payload,
            timeout=10
        )
        
        if derivative_response.status_code != 200:
            print(f"   ❌ Error en cálculo de derivadas: {derivative_response.status_code}")
            return False
        
        derivative_result = derivative_response.json()
        print(f"   ✅ Derivadas calculadas directamente")
        print(f"   Max aceleración: {derivative_result['max_acceleration']:.6f}")
        print(f"   Tendencia: {derivative_result['acceleration_trend']}")
        
        # Verificar que datos lineales se clasifiquen como estables
        if derivative_result['acceleration_trend'] == "ACTIVIDAD ESTABLE":
            print("   ✅ Datos lineales clasificados correctamente")
        else:
            print(f"   ❌ Datos lineales mal clasificados: {derivative_result['acceleration_trend']}")
            return False
        
        print("\n🎉 ¡TODAS LAS PRUEBAS DE INTEGRACIÓN PASARON!")
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ No se puede conectar al servidor. ¿Está ejecutándose en localhost:8000?")
        return False
    except requests.exceptions.Timeout:
        print("❌ Timeout en la conexión al servidor")
        return False
    except Exception as e:
        print(f"❌ Error inesperado: {str(e)}")
        return False

def main():
    """Función principal"""
    success = test_api_integration()
    
    if success:
        print("\n✅ INTEGRACIÓN EXITOSA - El sistema está funcionando correctamente")
        sys.exit(0)
    else:
        print("\n❌ INTEGRACIÓN FALLIDA - Revisar problemas")
        sys.exit(1)

if __name__ == "__main__":
    main()
