"""
Módulo para cálculo de derivadas paso a paso
Implementa cálculos de primera y segunda derivada para análisis sísmico
"""

import numpy as np
from typing import List, Dict, Any, Tuple
from datetime import datetime, timedelta
import logging
from scipy.interpolate import interp1d
from scipy.signal import savgol_filter

from models.volcanic_data import SeismicReading, DerivativeResult, CalculationStep

logger = logging.getLogger(__name__)

class DerivativeCalculator:
    """
    Calculadora de derivadas para análisis sísmico volcánico
    """
    
    def __init__(self):
        self.calculation_history = []
        self.smoothing_window = 5  # Ventana para suavizado
        
    def calculate_derivatives(
        self, 
        seismic_data: List[SeismicReading],
        show_steps: bool = True
    ) -> DerivativeResult:
        """
        Calcula primera y segunda derivada de los datos sísmicos
        
        Args:
            seismic_data: Lista de lecturas sísmicas
            show_steps: Si mostrar pasos de cálculo detallados
            
        Returns:
            DerivativeResult con las derivadas calculadas
        """
        logger.info(f"Calculando derivadas para {len(seismic_data)} puntos de datos")
        
        if len(seismic_data) < 3:
            raise ValueError("Se necesitan al menos 3 puntos para calcular derivadas")
        
        # Preparar datos
        time_points, magnitude_values = self._prepare_data(seismic_data)
        
        # Suavizar datos si es necesario
        if len(magnitude_values) > self.smoothing_window:
            magnitude_values = savgol_filter(
                magnitude_values, 
                window_length=self.smoothing_window,
                polyorder=2
            )
        
        calculation_steps = []
        
        # Calcular primera derivada
        first_derivative, steps1 = self._calculate_first_derivative(
            time_points, magnitude_values, show_steps
        )
        if show_steps:
            calculation_steps.extend(steps1)
        
        # Calcular segunda derivada
        second_derivative, steps2 = self._calculate_second_derivative(
            time_points, first_derivative, show_steps
        )
        if show_steps:
            calculation_steps.extend(steps2)
        
        # Calcular puntos de aceleración
        acceleration_points = self._calculate_acceleration_points(
            time_points, magnitude_values, first_derivative, second_derivative
        )
        
        # Analizar tendencia
        max_acceleration = float(np.max(np.abs(second_derivative)))
        acceleration_trend = self._analyze_acceleration_trend(second_derivative)
        
        if show_steps:
            calculation_steps.append({
                "step_number": len(calculation_steps) + 1,
                "description": "Análisis de aceleración máxima",
                "formula": "max_acceleration = max(|d²f/dt²|)",
                "input_values": {"second_derivative": second_derivative.tolist()},
                "calculation": f"max(|{second_derivative.tolist()}|)",
                "result": max_acceleration,
                "explanation": f"La máxima aceleración detectada es {max_acceleration:.4f}, indicando {acceleration_trend}"
            })
        
        result = DerivativeResult(
            first_derivative=first_derivative.tolist(),
            second_derivative=second_derivative.tolist(),
            acceleration_points=acceleration_points.tolist(),
            max_acceleration=max_acceleration,
            acceleration_trend=acceleration_trend,
            calculation_steps=calculation_steps if show_steps else None
        )
        
        self.calculation_history.append({
            "timestamp": datetime.utcnow(),
            "data_points": len(seismic_data),
            "max_acceleration": max_acceleration,
            "trend": acceleration_trend
        })
        
        logger.info(f"Derivadas calculadas: max_acceleration={max_acceleration:.4f}, trend={acceleration_trend}")
        return result
    
    def _prepare_data(self, seismic_data: List[SeismicReading]) -> Tuple[np.ndarray, np.ndarray]:
        """
        Prepara los datos para cálculo de derivadas
        """
        # Ordenar por timestamp
        sorted_data = sorted(seismic_data, key=lambda x: x.timestamp)
        
        # Extraer tiempo y magnitud
        start_time = sorted_data[0].timestamp
        time_points = np.array([
            (reading.timestamp - start_time).total_seconds() / 3600  # Convertir a horas
            for reading in sorted_data
        ])
        
        magnitude_values = np.array([reading.magnitude for reading in sorted_data])
        
        return time_points, magnitude_values
    
    def _calculate_first_derivative(
        self, 
        time_points: np.ndarray, 
        values: np.ndarray, 
        show_steps: bool
    ) -> Tuple[np.ndarray, List[Dict[str, Any]]]:
        """
        Calcula la primera derivada usando diferencias finitas
        df/dt = (f(t+h) - f(t-h)) / (2h)
        """
        steps = []
        first_derivative = np.zeros_like(values)
        
        for i in range(len(values)):
            if i == 0:
                # Forward difference para el primer punto
                h = time_points[1] - time_points[0]
                if h == 0: h = 1e-9 # Evitar división por cero
                derivative_val = (values[1] - values[0]) / h
                formula = "df/dt = (f(t+h) - f(t)) / h"
                calculation = f"({values[1]:.4f} - {values[0]:.4f}) / {h:.8f}"
                
            elif i == len(values) - 1:
                # Backward difference para el último punto
                h = time_points[i] - time_points[i-1]
                if h == 0: h = 1e-9 # Evitar división por cero
                derivative_val = (values[i] - values[i-1]) / h
                formula = "df/dt = (f(t) - f(t-h)) / h"
                calculation = f"({values[i]:.4f} - {values[i-1]:.4f}) / {h:.8f}"
                
            else:
                # Central difference para puntos intermedios
                h_forward = time_points[i+1] - time_points[i]
                h_backward = time_points[i] - time_points[i-1]
                h_avg = (h_forward + h_backward) / 2
                if h_avg == 0: h_avg = 1e-9 # Evitar división por cero
                
                derivative_val = (values[i+1] - values[i-1]) / (2 * h_avg)
                formula = "df/dt = (f(t+h) - f(t-h)) / (2h)"
                calculation = f"({values[i+1]:.4f} - {values[i-1]:.4f}) / (2 * {h_avg:.8f})"
            
            first_derivative[i] = derivative_val
            
            if show_steps and i < 5:  # Mostrar solo los primeros 5 pasos
                steps.append({
                    "step_number": i + 1,
                    "description": f"Primera derivada en punto {i+1}",
                    "formula": formula,
                    "input_values": {
                        "time": time_points[i],
                        "value": values[i],
                        "h": h_avg if i > 0 and i < len(values) - 1 else (h if i == 0 else h)
                    },
                    "calculation": calculation,
                    "result": derivative_val,
                    "explanation": f"La velocidad de cambio en t={time_points[i]:.2f}h es {derivative_val:.4f}"
                })
        
        return first_derivative, steps
    
    def _calculate_second_derivative(
        self, 
        time_points: np.ndarray, 
        first_derivative: np.ndarray, 
        show_steps: bool
    ) -> Tuple[np.ndarray, List[Dict[str, Any]]]:
        """
        Calcula la segunda derivada aplicando derivada a la primera derivada
        d²f/dt² = d(df/dt)/dt
        """
        steps = []
        second_derivative = np.zeros_like(first_derivative)
        
        for i in range(len(first_derivative)):
            if i == 0:
                # Forward difference
                h = time_points[1] - time_points[0]
                if h == 0: h = 1e-9 # Evitar división por cero
                derivative_val = (first_derivative[1] - first_derivative[0]) / h
                formula = "d²f/dt² = (f'(t+h) - f'(t)) / h"
                calculation = f"({first_derivative[1]:.4f} - {first_derivative[0]:.4f}) / {h:.8f}"
                
            elif i == len(first_derivative) - 1:
                # Backward difference
                h = time_points[i] - time_points[i-1]
                if h == 0: h = 1e-9 # Evitar división por cero
                derivative_val = (first_derivative[i] - first_derivative[i-1]) / h
                formula = "d²f/dt² = (f'(t) - f'(t-h)) / h"
                calculation = f"({first_derivative[i]:.4f} - {first_derivative[i-1]:.4f}) / {h:.8f}"
                
            else:
                # Central difference
                h_forward = time_points[i+1] - time_points[i]
                h_backward = time_points[i] - time_points[i-1]
                h_avg = (h_forward + h_backward) / 2
                if h_avg == 0: h_avg = 1e-9 # Evitar división por cero
                
                derivative_val = (first_derivative[i+1] - first_derivative[i-1]) / (2 * h_avg)
                formula = "d²f/dt² = (f'(t+h) - f'(t-h)) / (2h)"
                calculation = f"({first_derivative[i+1]:.4f} - {first_derivative[i-1]:.4f}) / (2 * {h_avg:.8f})"
            
            second_derivative[i] = derivative_val
            
            if show_steps and i < 3:  # Mostrar solo los primeros 3 pasos
                steps.append({
                    "step_number": len(steps) + 1,
                    "description": f"Segunda derivada en punto {i+1}",
                    "formula": formula,
                    "input_values": {
                        "time": time_points[i],
                        "first_derivative": first_derivative[i],
                        "h": h_avg if i > 0 and i < len(first_derivative) - 1 else (h if i == 0 else h)
                    },
                    "calculation": calculation,
                    "result": derivative_val,
                    "explanation": f"La aceleración en t={time_points[i]:.2f}h es {derivative_val:.4f}"
                })
        
        return second_derivative, steps
    
    def _calculate_acceleration_points(
        self, 
        time_points: np.ndarray, 
        values: np.ndarray,
        first_derivative: np.ndarray, 
        second_derivative: np.ndarray
    ) -> np.ndarray:
        """
        Calcula puntos de aceleración significativa
        """
        # Buscar puntos donde la segunda derivada es máxima
        threshold = np.std(second_derivative) * 2  # Umbral de 2 desviaciones estándar
        
        acceleration_points = []
        for i, accel in enumerate(second_derivative):
            if abs(accel) > threshold:
                acceleration_points.append(values[i])
            else:
                acceleration_points.append(0.0)
        
        return np.array(acceleration_points)
    
    def _analyze_acceleration_trend(self, second_derivative: np.ndarray) -> str:
        """
        Analiza la tendencia de aceleración
        """
        mean_acceleration = np.mean(second_derivative)
        std_acceleration = np.std(second_derivative)
        max_acceleration = np.max(np.abs(second_derivative))
        
        if max_acceleration > 3 * std_acceleration:
            if mean_acceleration > 0:
                return "ACELERACIÓN CRÍTICA POSITIVA"
            else:
                return "ACELERACIÓN CRÍTICA NEGATIVA"
        elif max_acceleration > 2 * std_acceleration:
            return "ACELERACIÓN MODERADA"
        elif max_acceleration > std_acceleration:
            return "ACELERACIÓN LEVE"
        else:
            return "ACTIVIDAD ESTABLE"
    
    def get_calculation_history(self) -> List[Dict[str, Any]]:
        """
        Retorna el historial de cálculos
        """
        return self.calculation_history
    
    def clear_history(self):
        """
        Limpia el historial de cálculos
        """
        self.calculation_history = []
        logger.info("Historial de cálculos limpiado")