"""
Failure Forecast Model (FFM) para predicción de erupciones volcánicas
Implementa el modelo de pronóstico de falla basado en aceleración de precursores
"""

import numpy as np
from typing import List, Dict, Any, Tuple, Optional
from datetime import datetime, timedelta
import logging
from scipy.optimize import curve_fit
from scipy.stats import linregress

from models.volcanic_data import SeismicReading, DerivativeResult, FFMResult

logger = logging.getLogger(__name__)

class FFMModel:
    """
    Modelo de Pronóstico de Falla (FFM) para predicción volcánica
    
    Basado en la teoría de que la actividad precursora sigue un patrón
    de aceleración que se puede modelar matemáticamente antes de la falla
    """
    
    def __init__(self):
        self.critical_thresholds = {
            'acceleration': 0.5,      # Umbral crítico de aceleración
            'frequency_increase': 2.0, # Aumento crítico en frecuencia
            'magnitude_trend': 0.3,    # Tendencia crítica de magnitud
            'time_clustering': 0.8     # Umbral de clustering temporal
        }
        
        self.model_parameters = {
            'alpha': 1.0,    # Parámetro de forma
            'beta': 0.5,     # Parámetro de escala
            'gamma': 2.0     # Parámetro de aceleración
        }
        
        self.analysis_history = []
    
    def analyze_failure_pattern(
        self,
        seismic_data: List[SeismicReading],
        derivative_results: DerivativeResult
    ) -> FFMResult:
        """
        Analiza el patrón de falla usando el modelo FFM
        
        Args:
            seismic_data: Datos sísmicos originales
            derivative_results: Resultados del análisis de derivadas
            
        Returns:
            FFMResult con la predicción de falla
        """
        logger.info("Iniciando análisis FFM")
        
        # Preparar datos temporales
        time_series = self._prepare_time_series(seismic_data)
        
        # Calcular métricas de aceleración
        acceleration_metrics = self._calculate_acceleration_metrics(
            derivative_results, time_series
        )
        
        # Aplicar modelo de falla
        failure_probability = self._calculate_failure_probability(
            acceleration_metrics, derivative_results
        )
        
        # Estimar tiempo hasta falla
        time_to_failure = self._estimate_time_to_failure(
            acceleration_metrics, time_series
        )
        
        # Evaluar tendencia de aceleración
        acceleration_trend = self._evaluate_acceleration_trend(
            derivative_results.second_derivative
        )
        
        # Calcular nivel de confianza
        confidence_level = self._calculate_confidence_level(
            acceleration_metrics, len(seismic_data)
        )
        
        # Evaluar umbral crítico
        critical_threshold = self._evaluate_critical_threshold(
            acceleration_metrics
        )
        
        # Identificar indicadores precursores
        precursor_indicators = self._identify_precursor_indicators(
            acceleration_metrics, derivative_results
        )
        
        result = FFMResult(
            failure_probability=failure_probability,
            time_to_failure=time_to_failure,
            acceleration_trend=acceleration_trend,
            confidence_level=confidence_level,
            critical_threshold=critical_threshold,
            precursor_indicators=precursor_indicators
        )
        
        # Guardar en historial
        self.analysis_history.append({
            'timestamp': datetime.utcnow(),
            'failure_probability': failure_probability,
            'time_to_failure': time_to_failure,
            'data_points': len(seismic_data)
        })
        
        logger.info(f"FFM completado: probabilidad={failure_probability:.2%}, "
                   f"tiempo_falla={time_to_failure}h, confianza={confidence_level:.2%}")
        
        return result
    
    def _prepare_time_series(self, seismic_data: List[SeismicReading]) -> np.ndarray:
        """
        Prepara serie temporal para análisis FFM
        """
        sorted_data = sorted(seismic_data, key=lambda x: x.timestamp)
        start_time = sorted_data[0].timestamp
        
        time_series = np.array([
            (reading.timestamp - start_time).total_seconds() / 3600
            for reading in sorted_data
        ])
        
        return time_series
    
    def _calculate_acceleration_metrics(
        self, 
        derivative_results: DerivativeResult,
        time_series: np.ndarray
    ) -> Dict[str, float]:
        """
        Calcula métricas de aceleración para FFM
        """
        metrics = {}
        
        # Aceleración máxima
        metrics['max_acceleration'] = derivative_results.max_acceleration
        
        # Aceleración promedio
        metrics['mean_acceleration'] = np.mean(np.abs(derivative_results.second_derivative))
        
        # Variabilidad de aceleración
        metrics['acceleration_variance'] = np.var(derivative_results.second_derivative)
        
        # Tendencia de aceleración (pendiente)
        if len(derivative_results.second_derivative) > 1:
            slope, intercept, r_value, p_value, std_err = linregress(
                time_series, derivative_results.second_derivative
            )
            metrics['acceleration_slope'] = slope
            metrics['acceleration_r_squared'] = r_value ** 2
        else:
            metrics['acceleration_slope'] = 0.0
            metrics['acceleration_r_squared'] = 0.0
        
        # Clustering temporal (concentración de eventos)
        metrics['temporal_clustering'] = self._calculate_temporal_clustering(time_series)
        
        # Intensidad creciente
        metrics['intensity_growth'] = self._calculate_intensity_growth(
            derivative_results.first_derivative
        )
        
        return metrics
    
    def _calculate_failure_probability(
        self, 
        acceleration_metrics: Dict[str, float],
        derivative_results: DerivativeResult
    ) -> float:
        """
        Calcula la probabilidad de falla usando modelo FFM
        
        P(falla) = 1 - exp(-λ * t^α)
        donde λ depende de la aceleración y α es el parámetro de forma
        """
        # Parámetro lambda basado en aceleración
        lambda_param = (
            acceleration_metrics['max_acceleration'] * 0.4 +
            acceleration_metrics['mean_acceleration'] * 0.3 +
            acceleration_metrics['acceleration_slope'] * 0.2 +
            acceleration_metrics['temporal_clustering'] * 0.1
        )
        
        # Normalizar lambda
        lambda_param = min(max(lambda_param, 0.0), 1.0)
        
        # Aplicar modelo de Weibull modificado
        alpha = self.model_parameters['alpha']
        probability = 1 - np.exp(-lambda_param * (1.0 ** alpha))
        
        # Ajustar según tendencia
        if derivative_results.acceleration_trend in ['ACELERACIÓN CRÍTICA POSITIVA', 'ACELERACIÓN CRÍTICA NEGATIVA']:
            probability *= 1.3
        elif derivative_results.acceleration_trend == 'ACELERACIÓN MODERADA':
            probability *= 1.1
        
        return min(probability, 1.0)
    
    def _estimate_time_to_failure(
        self,
        acceleration_metrics: Dict[str, float],
        time_series: np.ndarray
    ) -> Optional[float]:
        """
        Estima tiempo hasta falla basado en aceleración
        
        t_falla = t_0 * (1 + α * exp(-β * aceleración))
        """
        if acceleration_metrics['acceleration_slope'] <= 0:
            return None  # No hay tendencia aceleratoria
        
        # Tiempo base estimado
        time_base = 24.0  # 24 horas como base
        
        # Factor de aceleración
        accel_factor = acceleration_metrics['max_acceleration'] / self.critical_thresholds['acceleration']
        
        # Tiempo estimado hasta falla
        time_to_failure = time_base * np.exp(-accel_factor * self.model_parameters['beta'])
        
        # Ajustar según clustering temporal
        time_to_failure *= (1 - acceleration_metrics['temporal_clustering'] * 0.5)
        
        return max(time_to_failure, 0.5)  # Mínimo 0.5 horas
    
    def _evaluate_acceleration_trend(self, second_derivative: List[float]) -> str:
        """
        Evalúa la tendencia de aceleración
        """
        if len(second_derivative) < 2:
            return "DATOS INSUFICIENTES"
        
        second_deriv = np.array(second_derivative)
        recent_trend = np.mean(second_deriv[-5:]) if len(second_deriv) >= 5 else np.mean(second_deriv)
        overall_trend = np.mean(second_deriv)
        
        if recent_trend > 2 * overall_trend and recent_trend > 0.5:
            return "ACELERACIÓN EXPONENCIAL"
        elif recent_trend > overall_trend and recent_trend > 0.2:
            return "ACELERACIÓN CRECIENTE"
        elif abs(recent_trend) < 0.1:
            return "ESTABLE"
        else:
            return "DESACELERACIÓN"
    
    def _calculate_confidence_level(
        self, 
        acceleration_metrics: Dict[str, float],
        data_points: int
    ) -> float:
        """
        Calcula el nivel de confianza del modelo
        """
        # Confianza basada en cantidad de datos
        data_confidence = min(data_points / 50.0, 1.0)  # Máximo con 50 puntos
        
        # Confianza basada en R²
        r_squared_confidence = acceleration_metrics['acceleration_r_squared']
        
        # Confianza basada en consistencia de métricas
        consistency_confidence = 1.0 - acceleration_metrics['acceleration_variance'] / 10.0
        consistency_confidence = max(consistency_confidence, 0.0)
        
        # Confianza total
        total_confidence = (
            data_confidence * 0.4 +
            r_squared_confidence * 0.3 +
            consistency_confidence * 0.3
        )
        
        return min(total_confidence, 1.0)
    
    def _evaluate_critical_threshold(
        self, 
        acceleration_metrics: Dict[str, float]
    ) -> float:
        """
        Evalúa qué porcentaje del umbral crítico se ha alcanzado
        """
        thresholds_reached = []
        
        # Verificar cada umbral
        thresholds_reached.append(
            acceleration_metrics['max_acceleration'] / self.critical_thresholds['acceleration']
        )
        
        thresholds_reached.append(
            acceleration_metrics['temporal_clustering'] / self.critical_thresholds['time_clustering']
        )
        
        thresholds_reached.append(
            acceleration_metrics['intensity_growth'] / self.critical_thresholds['magnitude_trend']
        )
        
        # Retornar el máximo porcentaje alcanzado
        return min(max(thresholds_reached), 1.0)
    
    def _identify_precursor_indicators(
        self,
        acceleration_metrics: Dict[str, float],
        derivative_results: DerivativeResult
    ) -> List[str]:
        """
        Identifica indicadores precursores activos
        """
        indicators = []
        
        # Verificar aceleración crítica
        if acceleration_metrics['max_acceleration'] > self.critical_thresholds['acceleration']:
            indicators.append("ACELERACIÓN SÍSMICA CRÍTICA")
        
        # Verificar clustering temporal
        if acceleration_metrics['temporal_clustering'] > self.critical_thresholds['time_clustering']:
            indicators.append("CLUSTERING TEMPORAL ALTO")
        
        # Verificar tendencia creciente
        if acceleration_metrics['acceleration_slope'] > 0.1:
            indicators.append("TENDENCIA ACELERATORIA")
        
        # Verificar intensidad creciente
        if acceleration_metrics['intensity_growth'] > self.critical_thresholds['magnitude_trend']:
            indicators.append("INTENSIDAD CRECIENTE")
        
        # Verificar variabilidad
        if acceleration_metrics['acceleration_variance'] > 1.0:
            indicators.append("ALTA VARIABILIDAD")
        
        # Verificar patrón específico
        if (derivative_results.acceleration_trend == "ACELERACIÓN CRÍTICA POSITIVA" or
            derivative_results.acceleration_trend == "ACELERACIÓN CRÍTICA NEGATIVA"):
            indicators.append("PATRÓN CRÍTICO DETECTADO")
        
        return indicators
    
    def _calculate_temporal_clustering(self, time_series: np.ndarray) -> float:
        """
        Calcula el clustering temporal de eventos
        """
        if len(time_series) < 2:
            return 0.0
        
        # Calcular intervalos entre eventos
        intervals = np.diff(time_series)
        
        # Calcular coeficiente de variación
        if np.mean(intervals) > 0:
            cv = np.std(intervals) / np.mean(intervals)
            clustering = 1.0 - min(cv, 1.0)  # Invertir para que clustering alto = 1
        else:
            clustering = 0.0
        
        return clustering
    
    def _calculate_intensity_growth(self, first_derivative: List[float]) -> float:
        """
        Calcula el crecimiento de intensidad
        """
        if len(first_derivative) < 2:
            return 0.0
        
        first_deriv = np.array(first_derivative)
        
        # Calcular tendencia de la primera derivada
        if len(first_deriv) > 1:
            slope, _, _, _, _ = linregress(range(len(first_deriv)), first_deriv)
            return max(slope, 0.0)  # Solo crecimiento positivo
        
        return 0.0
    
    def get_analysis_history(self) -> List[Dict[str, Any]]:
        """
        Retorna el historial de análisis FFM
        """
        return self.analysis_history
    
    def clear_history(self):
        """
        Limpia el historial de análisis
        """
        self.analysis_history = []
        logger.info("Historial de análisis FFM limpiado")
    
    def update_parameters(self, new_parameters: Dict[str, float]):
        """
        Actualiza los parámetros del modelo
        """
        self.model_parameters.update(new_parameters)
        logger.info(f"Parámetros del modelo actualizados: {self.model_parameters}")
    
    def update_thresholds(self, new_thresholds: Dict[str, float]):
        """
        Actualiza los umbrales críticos
        """
        self.critical_thresholds.update(new_thresholds)
        logger.info(f"Umbrales críticos actualizados: {self.critical_thresholds}")