# Cálculo de Derivadas para Predicción Volcánica
## Explicación Para Estudiantes de Ingeniería (y Perros Perdidos) 🐕🌋

## ¿Qué son las Derivadas en el Contexto Volcánico?

### Conceptos Básicos
Una **derivada** mide qué tan rápido cambia algo con respecto al tiempo. En vulcanología:

- **Función original f(t)**: Magnitud de la actividad sísmica en el tiempo
- **Primera derivada f'(t)**: Velocidad de cambio de la actividad sísmica  
- **Segunda derivada f''(t)**: Aceleración del cambio de la actividad sísmica

### Analogía del Automóvil 🚗
- **Posición**: Magnitud sísmica actual
- **Velocidad**: Primera derivada (¿aumenta o disminuye la actividad?)
- **Aceleración**: Segunda derivada (¿el cambio se acelera o desacelera?)

## Datos de Entrada

### Estructura de Datos Sísmicos
```python
SeismicReading {
    timestamp: datetime,     # Momento de la lectura
    magnitude: float,        # Magnitud del evento sísmico
    frequency: float,        # Frecuencia del evento
    duration: float,         # Duración del evento  
    depth: float            # Profundidad del hipocentro
}
```

### Preparación de Datos
1. **Ordenamiento temporal**: Los datos se ordenan por `timestamp`
2. **Conversión temporal**: Se convierte el tiempo a horas desde el primer evento
3. **Extracción de magnitudes**: Se toma solo la magnitud para el cálculo

**Ejemplo de preparación:**
```
Tiempo original: [2025-07-07 01:48:23, 2025-07-07 02:48:23, ...]
Tiempo procesado: [0.0, 1.0, 2.0, 3.0, ...] (horas)
Magnitudes: [1.75, 2.05, 2.31, 1.46, ...]
```

## Cálculo de Primera Derivada

### Métodos de Diferencias Finitas

La aplicación usa tres métodos según la posición del punto:

#### 1. Diferencia Hacia Adelante (Primer punto)
```
f'(t₀) = [f(t₁) - f(t₀)] / (t₁ - t₀)
```

**Ejemplo:**
```
Punto 1: df/dt = (2.7050 - 2.8579) / 1.0000 = -0.1529
```
**Interpretación**: La actividad sísmica está disminuyendo a razón de 0.1529 unidades por hora.

#### 2. Diferencia Hacia Atrás (Último punto)  
```
f'(tₙ) = [f(tₙ) - f(tₙ₋₁)] / (tₙ - tₙ₋₁)
```

#### 3. Diferencia Central (Puntos intermedios)
```
f'(tᵢ) = [f(tᵢ₊₁) - f(tᵢ₋₁)] / [2 * h]
```
donde `h` es el paso de tiempo promedio.

**Ejemplo del código:**
```
Punto 2: df/dt = (2.3312 - 2.8579) / (2 * 1.5000) = -0.1756
```

### ¿Por qué Diferencia Central?
- **Mayor precisión**: Usa información de ambos lados del punto
- **Menor error**: El error es de orden O(h²) vs O(h) de las otras
- **Mejor estabilidad**: Menos sensible a ruido en los datos

## Cálculo de Segunda Derivada

### Aplicación del Mismo Principio
La segunda derivada se calcula aplicando diferencias finitas **a la primera derivada**:

```
f''(t) = d[f'(t)]/dt
```

**Ejemplo:**
Si las primeras derivadas son: `[-0.1529, -0.1756, -0.1806]`
```
f''(t₁) = (-0.1756 - (-0.1529)) / 1.0 = -0.0227
```

### Interpretación Física
- **f''(t) > 0**: La velocidad de cambio está aumentando (aceleración positiva)
- **f''(t) < 0**: La velocidad de cambio está disminuyendo (desaceleración)
- **|f''(t)| grande**: Cambios muy rápidos en la velocidad

## Suavizado de Datos

### Filtro Savitzky-Golay
Antes del cálculo, la aplicación aplica un filtro para reducir ruido:

```python
if len(magnitude_values) > 5:
    magnitude_values = savgol_filter(
        magnitude_values, 
        window_length=5,    # Ventana de 5 puntos
        polyorder=2         # Polinomio de grado 2
    )
```

**¿Por qué?**
- **Reduce ruido**: Los datos sísmicos reales tienen mucho ruido
- **Preserva tendencias**: No distorsiona patrones importantes
- **Mejora precisión**: Las derivadas son más estables

## Análisis de Resultados

### Puntos de Aceleración
```python
threshold = np.std(second_derivative) * 2
```
Se consideran puntos críticos donde `|f''(t)| > 2σ`

### Clasificación de Tendencias

| Condición | Tendencia |
|-----------|-----------|
| `max(|f''|) > 3σ` y `mean(f'') > 0` | ACELERACIÓN CRÍTICA POSITIVA |
| `max(|f''|) > 3σ` y `mean(f'') < 0` | ACELERACIÓN CRÍTICA NEGATIVA |
| `max(|f''|) > 2σ` | ACELERACIÓN MODERADA |
| `max(|f''|) > σ` | ACELERACIÓN LEVE |
| `max(|f''|) ≤ σ` | ACTIVIDAD ESTABLE |

## Ejemplo Completo de Cálculo

### Datos de Entrada
```
Tiempo (h): [0.0, 1.0, 3.0, 7.0, 10.0]
Magnitud:   [2.86, 2.71, 2.33, 1.02, 1.27]
```

### Primera Derivada
```
Punto 1: f'(0) = (2.71 - 2.86) / 1.0 = -0.15
Punto 2: f'(1) = (2.33 - 2.86) / (2*1.5) = -0.18
Punto 3: f'(3) = (1.02 - 2.71) / (2*3.0) = -0.28
...
```

### Segunda Derivada  
```
Punto 1: f''(0) = (-0.18 - (-0.15)) / 1.0 = -0.03
Punto 2: f''(1) = (-0.28 - (-0.15)) / (2*1.5) = -0.04
...
```

### Interpretación Volcánica
- **f'(t) < 0**: La actividad sísmica está disminuyendo
- **f''(t) < 0**: La disminución se está acelerando (el volcán se está "calmando" más rápido)

## Limitaciones y Consideraciones

### Errores Numéricos
- **Datos ruidosos**: Las derivadas amplifican el ruido
- **Espaciado irregular**: Puede introducir errores en el cálculo
- **Pocos puntos**: Se necesitan mínimo 3 puntos para calcular derivadas

### Validación Física
- **Coherencia temporal**: Los resultados deben tener sentido físico
- **Magnitudes razonables**: Las derivadas no deben ser excesivamente grandes
- **Tendencias consistentes**: Deben correlacionar con otros indicadores

## Aplicación en Predicción Volcánica

### Indicadores de Alerta
1. **Primera derivada positiva creciente**: Aumento sostenido de actividad
2. **Segunda derivada alta**: Cambios acelerados (posible erupción inminente)
3. **Patrones específicos**: Secuencias características pre-eruptivas

### Integración con FFM (Failure Forecast Model)
Las derivadas calculadas se usan como entrada para el modelo FFM que predice:
- **Tiempo hasta falla**: Cuándo podría ocurrir la erupción
- **Probabilidad de falla**: Qué tan probable es la erupción
- **Nivel de confianza**: Qué tan confiables son las predicciones

## Código de Ejemplo Simplificado

```python
def calcular_derivada_simple(tiempo, magnitudes):
    """Versión simplificada del cálculo de primera derivada"""
    derivada = []
    
    for i in range(len(magnitudes)):
        if i == 0:
            # Primer punto: diferencia hacia adelante
            dt = tiempo[1] - tiempo[0]
            dx = magnitudes[1] - magnitudes[0]
            derivada.append(dx / dt)
        elif i == len(magnitudes) - 1:
            # Último punto: diferencia hacia atrás  
            dt = tiempo[i] - tiempo[i-1]
            dx = magnitudes[i] - magnitudes[i-1]
            derivada.append(dx / dt)
        else:
            # Punto medio: diferencia central
            dt = tiempo[i+1] - tiempo[i-1]
            dx = magnitudes[i+1] - magnitudes[i-1]
            derivada.append(dx / dt)
    
    return derivada
```

## Conclusiones

El cálculo de derivadas en esta aplicación:

1. **Transforma datos sísmicos** en información sobre velocidad y aceleración del cambio
2. **Usa métodos numéricos robustos** (diferencias finitas con suavizado)
3. **Proporciona indicadores cuantitativos** para la predicción volcánica
4. **Se integra con modelos predictivos** para generar alertas tempranas

La clave está en entender que **no solo importa QUÉ está pasando** (magnitud sísmica), sino **QUÉ TAN RÁPIDO está cambiando** (primera derivada) y **SI ESE CAMBIO SE ESTÁ ACELERANDO** (segunda derivada).

Para un volcán, una aceleración crítica positiva significa que los eventos sísmicos no solo están aumentando, sino que están aumentando cada vez más rápido - una señal de alerta máxima para una posible erupción inminente. 🌋⚠️
