/**
 * 🌋 Volcano App Frontend - Gráfico de Derivadas
 * Visualización de datos sísmicos, primera y segunda derivada
 */

import React from 'react';
import {
  Area,
  AreaChart,
  CartesianGrid,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
  ReferenceLine,
  Legend
} from 'recharts';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { DerivativeResult } from '../../services/calculoService';

interface DerivativeChartProps {
  derivativeResult: DerivativeResult;
  showSteps?: boolean;
}

interface ChartDataPoint {
  time: number;
  original: number;
  firstDerivative: number;
  secondDerivative: number;
  accelerationZone?: boolean;
}

export const DerivativeChart: React.FC<DerivativeChartProps> = ({ 
  derivativeResult, 
  showSteps = false 
}) => {
  // Preparar datos para el gráfico
  const chartData: ChartDataPoint[] = derivativeResult.first_derivative.map((firstDer, index) => ({
    time: index,
    original: 0, // No disponible en la respuesta actual
    firstDerivative: firstDer,
    secondDerivative: derivativeResult.second_derivative[index] || 0,
    accelerationZone: derivativeResult.second_derivative[index] > 0
  }));

  // Detectar zonas de aceleración
  const accelerationZones = chartData.filter(d => d.accelerationZone);
  const maxAcceleration = Math.max(...derivativeResult.second_derivative);
  const hasPositiveAcceleration = maxAcceleration > 0;

  // Detectar aceleración y tendencia localmente
  const accelerationDetected = accelerationZones.length > 0;
  const recentValues = derivativeResult.second_derivative.slice(-5);
  const trend = recentValues.length > 1 && 
    recentValues[recentValues.length - 1] > recentValues[0] ? 'increasing' : 'decreasing';

  // Colores para las series
  const colors = {
    original: '#3b82f6',      // Azul
    firstDerivative: '#10b981', // Verde
    secondDerivative: '#ef4444', // Rojo
    accelerationZone: '#fbbf24' // Amarillo
  };

  // Tooltip personalizado
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white border border-gray-300 rounded-lg p-3 shadow-lg">
          <p className="font-semibold">{`Tiempo: ${label}`}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} style={{ color: entry.color }} className="text-sm">
              {`${entry.name}: ${entry.value.toFixed(3)}`}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  return (
    <div className="space-y-6">
      {/* Resumen de resultados */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            📊 Análisis de Derivadas
            {accelerationDetected && (
              <Badge variant="destructive">Aceleración Detectada</Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {derivativeResult.first_derivative.length}
              </div>
              <div className="text-sm text-gray-600">Puntos de Datos</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {maxAcceleration.toFixed(3)}
              </div>
              <div className="text-sm text-gray-600">Máx. Aceleración</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">
                {accelerationZones.length}
              </div>
              <div className="text-sm text-gray-600">Zonas de Aceleración</div>
            </div>
          </div>

          <div className="flex items-center gap-2 mb-4">
            <Badge variant={trend === 'increasing' ? 'destructive' : 'default'}>
              Tendencia: {trend}
            </Badge>
            {hasPositiveAcceleration && (
              <Badge variant="outline" className="text-yellow-600">
                Presurización del Sistema
              </Badge>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Gráfico Principal */}
      <Card>
        <CardHeader>
          <CardTitle>Análisis de Derivadas Sísmicas</CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={400}>
            <LineChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="time" 
                label={{ value: 'Tiempo', position: 'insideBottom', offset: -5 }}
              />
              <YAxis 
                label={{ value: 'Magnitud', angle: -90, position: 'insideLeft' }}
              />
              <Tooltip content={<CustomTooltip />} />
              <Legend />
              
              {/* Línea de referencia en cero para derivadas */}
              <ReferenceLine y={0} stroke="#666" strokeDasharray="2 2" />
              
              {/* Series de datos - Solo mostramos derivadas ya que los originales no están disponibles */}
              <Line 
                type="monotone" 
                dataKey="firstDerivative" 
                stroke={colors.firstDerivative} 
                strokeWidth={2}
                name="Primera Derivada (Velocidad)"
                dot={false}
              />
              <Line 
                type="monotone" 
                dataKey="secondDerivative" 
                stroke={colors.secondDerivative} 
                strokeWidth={2}
                name="Segunda Derivada (Aceleración)"
                dot={false}
              />
            </LineChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Gráfico de Área para Aceleración */}
      {hasPositiveAcceleration && (
        <Card>
          <CardHeader>
            <CardTitle className="text-red-600">⚠️ Zonas de Aceleración Positiva</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="time" />
                <YAxis />
                <Tooltip content={<CustomTooltip />} />
                <Legend />
                
                {/* Área solo para valores positivos de segunda derivada */}
                <Area
                  type="monotone"
                  dataKey="secondDerivative"
                  stroke={colors.secondDerivative}
                  fill={colors.secondDerivative}
                  fillOpacity={0.3}
                  name="Aceleración Positiva"
                />
                
                {/* Línea de referencia en cero */}
                <ReferenceLine y={0} stroke="#666" strokeDasharray="2 2" />
              </AreaChart>
            </ResponsiveContainer>
            
            <div className="mt-4 p-4 bg-red-50 rounded-lg">
              <h4 className="font-semibold text-red-800 mb-2">
                🚨 Interpretación: Aceleración Positiva Detectada
              </h4>
              <p className="text-red-700 text-sm">
                Una segunda derivada positiva y creciente (S''(t) &gt; 0) indica una aceleración en la 
                liberación de energía sísmica. Según Kilburn (2018), esto puede interpretarse como 
                un indicio de presurización del sistema magmático, siendo una señal temprana de 
                un evento volcánico inminente.
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Pasos de Cálculo */}
      {showSteps && derivativeResult.calculation_steps && (
        <Card>
          <CardHeader>
            <CardTitle>📝 Pasos de Cálculo Detallados</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {derivativeResult.calculation_steps.map((step, index) => (
                <div key={index} className="border-l-4 border-blue-500 pl-4 py-2">
                  <div className="flex items-center gap-2 mb-1">
                    <Badge variant="outline">{`Paso ${step.step_number}`}</Badge>
                    <span className="font-semibold">{step.description}</span>
                  </div>
                  <div className="text-sm text-gray-600 space-y-1">
                    <div><strong>Fórmula:</strong> <code className="bg-gray-100 px-2 py-1 rounded">{step.formula}</code></div>
                    <div><strong>Cálculo:</strong> <code className="bg-gray-100 px-2 py-1 rounded">{step.calculation}</code></div>
                    <div><strong>Resultado:</strong> <span className="font-mono text-blue-600">{step.result}</span></div>
                    <div><strong>Explicación:</strong> {step.explanation}</div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Fórmulas Teóricas */}
      <Card>
        <CardHeader>
          <CardTitle>📐 Fórmulas Utilizadas</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="bg-blue-50 p-3 rounded-lg">
              <h4 className="font-semibold text-blue-800 mb-1">Primera Derivada (Velocidad)</h4>
              <code className="text-blue-700">S'(t) = S(t) - S(t-1)</code>
              <p className="text-sm text-blue-600 mt-1">
                Representa la velocidad de cambio de la actividad sísmica
              </p>
            </div>
            
            <div className="bg-green-50 p-3 rounded-lg">
              <h4 className="font-semibold text-green-800 mb-1">Segunda Derivada (Aceleración)</h4>
              <code className="text-green-700">S''(t) = S(t) - 2S(t-1) + S(t-2)</code>
              <p className="text-sm text-green-600 mt-1">
                Representa la aceleración/desaceleración de la actividad sísmica
              </p>
            </div>
            
            <div className="bg-red-50 p-3 rounded-lg">
              <h4 className="font-semibold text-red-800 mb-1">Señal Crítica</h4>
              <code className="text-red-700">S''(t) &gt; 0 y creciente</code>
              <p className="text-sm text-red-600 mt-1">
                Indica presurización del sistema magmático y posible evento volcánico inminente
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default DerivativeChart;