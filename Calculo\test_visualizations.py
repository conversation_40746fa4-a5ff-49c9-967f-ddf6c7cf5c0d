#!/usr/bin/env python3
"""
Script de prueba para verificar las visualizaciones
Verifica que los archivos HTML se han generado correctamente
"""

import os
from pathlib import Path
import webbrowser

def test_html_files():
    """Verifica que los archivos HTML existen y contienen datos"""
    viz_dir = Path("visualizations")
    
    expected_files = [
        "seismic_dashboard.html",
        "dashboard_normal.html", 
        "dashboard_pre_eruption.html",
        "dashboard_critical.html"
    ]
    
    print("🧪 VERIFICANDO ARCHIVOS DE VISUALIZACIÓN")
    print("="*50)
    
    all_good = True
    
    for filename in expected_files:
        file_path = viz_dir / filename
        
        if file_path.exists():
            file_size = file_path.stat().st_size
            
            # Leer contenido para verificar que tiene datos
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Verificaciones básicas
            has_d3 = "d3.v7.min.js" in content or "d3js.org" in content
            has_data = "seismicData" in content and "derivativeData" in content
            has_functions = "showChart" in content and "updateStats" in content
            has_styles = "background: linear-gradient" in content
            
            status = "✅" if has_d3 and has_data and has_functions and has_styles else "❌"
            
            print(f"{status} {filename}")
            print(f"   📁 Tamaño: {file_size:,} bytes")
            print(f"   📊 D3.js: {'✅' if has_d3 else '❌'}")
            print(f"   📈 Datos: {'✅' if has_data else '❌'}")
            print(f"   🔧 Funciones: {'✅' if has_functions else '❌'}")
            print(f"   🎨 Estilos: {'✅' if has_styles else '❌'}")
            
            if not (has_d3 and has_data and has_functions and has_styles):
                all_good = False
                
        else:
            print(f"❌ {filename} - NO ENCONTRADO")
            all_good = False
        
        print()
    
    return all_good

def test_javascript_syntax():
    """Verifica que no hay errores obvios de sintaxis en JavaScript"""
    viz_dir = Path("visualizations")
    dashboard_file = viz_dir / "seismic_dashboard.html"
    
    print("🔍 VERIFICANDO SINTAXIS JAVASCRIPT")
    print("="*40)
    
    if not dashboard_file.exists():
        print("❌ Archivo principal no encontrado")
        return False
    
    with open(dashboard_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Extraer la sección de JavaScript
    script_start = content.find("<script>")
    script_end = content.find("</script>", script_start)
    
    if script_start == -1 or script_end == -1:
        print("❌ No se encontró sección JavaScript")
        return False
    
    js_content = content[script_start:script_end]
    
    # Verificaciones básicas de sintaxis
    checks = {
        "Variables inicializadas": "let seismicData = " in js_content,
        "Funciones definidas": "function showChart" in js_content,
        "Event listeners": "addEventListener" in js_content,
        "No referencias undefined": not any(x in js_content for x in ["DERIVATIVE_[", "_PLACEHOLDER", "undefined"]),
        "Manejo de eventos": "function toggleRealTime(event)" in js_content
    }
    
    all_passed = True
    for check_name, passed in checks.items():
        status = "✅" if passed else "❌"
        print(f"{status} {check_name}")
        if not passed:
            all_passed = False
    
    return all_passed

def open_test_dashboard():
    """Abre el dashboard principal para prueba visual"""
    viz_dir = Path("visualizations")
    dashboard_file = viz_dir / "seismic_dashboard.html"
    
    if dashboard_file.exists():
        abs_path = os.path.abspath(dashboard_file)
        print(f"\n🚀 Abriendo dashboard de prueba: {abs_path}")
        webbrowser.open(f"file://{abs_path}")
        return True
    else:
        print("\n❌ No se puede abrir dashboard - archivo no encontrado")
        return False

def main():
    """Función principal de prueba"""
    print("🧪 SUITE DE PRUEBAS - VISUALIZACIONES SÍSMICAS")
    print("   Verificando integridad y funcionalidad de los dashboards")
    print()
    
    # Test 1: Verificar archivos
    test1_passed = test_html_files()
    
    # Test 2: Verificar JavaScript
    test2_passed = test_javascript_syntax()
    
    print("\n📋 RESUMEN DE PRUEBAS")
    print("="*30)
    print(f"{'✅' if test1_passed else '❌'} Archivos HTML")
    print(f"{'✅' if test2_passed else '❌'} Sintaxis JavaScript")
    
    overall_status = test1_passed and test2_passed
    
    if overall_status:
        print("\n🎉 ¡TODAS LAS PRUEBAS PASARON!")
        print("   Las visualizaciones están listas para usar")
        
        # Ofrecer abrir dashboard
        user_input = input("\n¿Deseas abrir el dashboard principal? (y/n): ").strip().lower()
        if user_input in ['y', 'yes', 's', 'si', '']:
            open_test_dashboard()
    else:
        print("\n⚠️  ALGUNAS PRUEBAS FALLARON")
        print("   Revisa los errores arriba y regenera las visualizaciones")
        
    print("\n💡 CONSEJOS:")
    print("   • Si hay errores, ejecuta: python seismic_visualizer.py")
    print("   • Para acceso rápido: python open_visualizations.py")
    print("   • Los archivos están en: visualizations/")

if __name__ == "__main__":
    main()
