#!/usr/bin/env python3
"""
Script de diagnóstico para verificar problemas de reemplazo
"""

import json
from pathlib import Path

def debug_placeholder_replacement():
    """Diagnostica problemas con el reemplazo de placeholders"""
    
    viz_dir = Path("visualizations")
    dashboard_file = viz_dir / "seismic_dashboard.html"
    
    if not dashboard_file.exists():
        print("❌ Archivo dashboard no encontrado")
        return
    
    with open(dashboard_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("🔍 DIAGNÓSTICO DE REEMPLAZO DE PLACEHOLDERS")
    print("="*50)
    
    # Buscar placeholders problemáticos
    problematic_patterns = [
        "DERIVATIVE_[",
        "DERIVATIVE_DATA_PLACEHOLDER", 
        "FFM_DATA_PLACEHOLDER",
        "ALERT_DATA_PLACEHOLDER",
        "DATA_PLACEHOLDER"
    ]
    
    for pattern in problematic_patterns:
        if pattern in content:
            lines = content.split('\n')
            for i, line in enumerate(lines, 1):
                if pattern in line:
                    print(f"❌ Línea {i}: {pattern}")
                    print(f"   Contenido: {line.strip()[:100]}...")
                    # Mostrar contexto
                    if i > 1:
                        print(f"   Anterior: {lines[i-2].strip()[:80]}...")
                    if i < len(lines):
                        print(f"   Siguiente: {lines[i].strip()[:80]}...")
                    print()
        else:
            print(f"✅ {pattern} - No encontrado")
    
    # Verificar estructura de datos
    print("\n📊 VERIFICACIÓN DE ESTRUCTURA DE DATOS")
    print("="*40)
    
    # Buscar inicio de cada variable
    script_start = content.find("<script>")
    script_end = content.find("</script>", script_start)
    
    if script_start != -1 and script_end != -1:
        js_content = content[script_start:script_end]
        
        # Buscar cada variable
        variables = ["seismicData", "derivativeData", "ffmData", "alertData"]
        
        for var in variables:
            var_pattern = f"let {var} = "
            if var_pattern in js_content:
                # Encontrar la línea completa
                start_idx = js_content.find(var_pattern)
                line_start = js_content.rfind('\n', 0, start_idx) + 1
                line_end = js_content.find('\n', start_idx)
                if line_end == -1:
                    line_end = len(js_content)
                
                line_content = js_content[line_start:line_end].strip()
                print(f"✅ {var}: {line_content[:80]}...")
                
                # Verificar si es JSON válido
                try:
                    # Extraer la parte JSON
                    json_start = line_content.find('[') or line_content.find('{')
                    if json_start != -1:
                        # Encontrar el final del JSON
                        json_part = line_content[json_start:]
                        if json_part.endswith(" || [];"):
                            json_part = json_part[:-6]
                        elif json_part.endswith(" || {};"):
                            json_part = json_part[:-6]
                        
                        json.loads(json_part)
                        print(f"   📋 JSON válido")
                except json.JSONDecodeError as e:
                    print(f"   ❌ JSON inválido: {e}")
            else:
                print(f"❌ {var}: Variable no encontrada")
    
    print("\n🔧 RECOMENDACIONES:")
    print("1. Regenerar visualizaciones con depuración")
    print("2. Verificar orden de reemplazo de placeholders") 
    print("3. Comprobar caracteres especiales en datos")

if __name__ == "__main__":
    debug_placeholder_replacement()
