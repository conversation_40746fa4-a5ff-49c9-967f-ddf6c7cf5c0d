"""
Sistema de Alertas Tempranas para Predicción Volcánica
Genera alertas basadas en análisis FFM y derivadas
"""

import uuid
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import logging

from models.volcanic_data import AlertLevel, AlertMessage, FFMResult

logger = logging.getLogger(__name__)

class AlertSystem:
    """
    Sistema de alertas tempranas para erupciones volcánicas
    """
    
    def __init__(self):
        self.active_alerts: List[AlertMessage] = []
        self.alert_history: List[AlertMessage] = []
        
        # Umbrales para diferentes niveles de alerta
        self.alert_thresholds = {
            AlertLevel.GREEN: {
                'probability_max': 0.15,
                'acceleration_max': 0.3,
                'time_to_failure_min': 72,  # horas
                'precursor_count_max': 1
            },
            AlertLevel.YELLOW: {
                'probability_max': 0.35,
                'acceleration_max': 0.6,
                'time_to_failure_min': 48,
                'precursor_count_max': 2
            },
            AlertLevel.ORANGE: {
                'probability_max': 0.65,
                'acceleration_max': 1.0,
                'time_to_failure_min': 24,
                'precursor_count_max': 3
            },
            AlertLevel.RED: {
                'probability_max': 1.0,
                'acceleration_max': float('inf'),
                'time_to_failure_min': 0,
                'precursor_count_max': float('inf')
            }
        }
        
        # Mensajes estándar para cada nivel
        self.alert_messages = {
            AlertLevel.GREEN: {
                'title': 'Actividad Normal',
                'description': 'Actividad volcánica dentro de parámetros normales',
                'recommendations': [
                    'Continuar monitoreo rutinario',
                    'Mantener sistemas de vigilancia activos',
                    'Revisar protocolos de emergencia'
                ]
            },
            AlertLevel.YELLOW: {
                'title': 'Precaución - Actividad Incrementada',
                'description': 'Detectado aumento en actividad sísmica precursora',
                'recommendations': [
                    'Incrementar frecuencia de monitoreo',
                    'Alertar a autoridades locales',
                    'Revisar planes de evacuación',
                    'Informar a comunidades cercanas'
                ]
            },
            AlertLevel.ORANGE: {
                'title': 'Alerta - Actividad Crítica',
                'description': 'Actividad sísmica indica posible erupción inminente',
                'recommendations': [
                    'Activar protocolos de emergencia',
                    'Preparar evacuación de zonas de riesgo',
                    'Coordinar con autoridades de emergencia',
                    'Monitoreo continuo 24/7',
                    'Alertar a medios de comunicación'
                ]
            },
            AlertLevel.RED: {
                'title': 'Emergencia - Erupción Inminente',
                'description': 'Indicadores críticos sugieren erupción inmediata',
                'recommendations': [
                    'EVACUACIÓN INMEDIATA de zonas de riesgo',
                    'Activar centros de emergencia',
                    'Coordinar con fuerzas armadas',
                    'Cerrar espacios aéreos',
                    'Activar albergues temporales',
                    'Comunicación masiva de emergencia'
                ]
            }
        }
    
    def evaluate_risk(self, ffm_results: FFMResult) -> AlertLevel:
        """
        Evalúa el riesgo y determina el nivel de alerta
        
        Args:
            ffm_results: Resultados del análisis FFM
            
        Returns:
            Nivel de alerta correspondiente
        """
        logger.info(f"Evaluando riesgo: probabilidad={ffm_results.failure_probability:.2%}")
        
        # Extraer métricas clave
        probability = ffm_results.failure_probability
        precursor_count = len(ffm_results.precursor_indicators)
        time_to_failure = ffm_results.time_to_failure or float('inf')
        
        # Determinar nivel de alerta
        alert_level = self._determine_alert_level(
            probability, precursor_count, time_to_failure
        )
        
        # Crear o actualizar alerta
        self._create_or_update_alert(alert_level, ffm_results)
        
        logger.info(f"Nivel de alerta determinado: {alert_level.value}")
        return alert_level
    
    def _determine_alert_level(
        self, 
        probability: float, 
        precursor_count: int, 
        time_to_failure: float
    ) -> AlertLevel:
        """
        Determina el nivel de alerta basado en métricas
        """
        # Verificar RED (máxima prioridad)
        if (probability > self.alert_thresholds[AlertLevel.ORANGE]['probability_max'] or
            time_to_failure < self.alert_thresholds[AlertLevel.RED]['time_to_failure_min'] or
            precursor_count >= 4):
            return AlertLevel.RED
        
        # Verificar ORANGE
        if (probability > self.alert_thresholds[AlertLevel.YELLOW]['probability_max'] or
            time_to_failure < self.alert_thresholds[AlertLevel.ORANGE]['time_to_failure_min'] or
            precursor_count >= self.alert_thresholds[AlertLevel.ORANGE]['precursor_count_max']):
            return AlertLevel.ORANGE
        
        # Verificar YELLOW
        if (probability > self.alert_thresholds[AlertLevel.GREEN]['probability_max'] or
            time_to_failure < self.alert_thresholds[AlertLevel.YELLOW]['time_to_failure_min'] or
            precursor_count >= self.alert_thresholds[AlertLevel.YELLOW]['precursor_count_max']):
            return AlertLevel.YELLOW
        
        # Por defecto GREEN
        return AlertLevel.GREEN
    
    def _create_or_update_alert(self, alert_level: AlertLevel, ffm_results: FFMResult):
        """
        Crea o actualiza una alerta
        """
        # Verificar si ya existe una alerta activa del mismo nivel
        existing_alert = self._find_active_alert(alert_level)
        
        if existing_alert:
            # Actualizar alerta existente
            existing_alert.timestamp = datetime.utcnow()
            existing_alert.probability = ffm_results.failure_probability
            logger.info(f"Alerta {alert_level.value} actualizada")
        else:
            # Crear nueva alerta
            alert_message = self._generate_alert_message(alert_level, ffm_results)
            
            # Desactivar alertas de nivel inferior
            self._deactivate_lower_level_alerts(alert_level)
            
            # Agregar nueva alerta
            self.active_alerts.append(alert_message)
            self.alert_history.append(alert_message)
            
            logger.info(f"Nueva alerta {alert_level.value} creada: {alert_message.id}")
    
    def _generate_alert_message(
        self, 
        alert_level: AlertLevel, 
        ffm_results: FFMResult
    ) -> AlertMessage:
        """
        Genera un mensaje de alerta completo
        """
        alert_config = self.alert_messages[alert_level]
        
        # Construir mensaje personalizado
        message = self._build_custom_message(alert_level, ffm_results)
        
        # Generar recomendaciones específicas
        recommendations = self._generate_specific_recommendations(alert_level, ffm_results)
        
        return AlertMessage(
            id=str(uuid.uuid4()),
            level=alert_level,
            message=message,
            timestamp=datetime.utcnow(),
            probability=ffm_results.failure_probability,
            recommendations=recommendations,
            active=True
        )
    
    def _build_custom_message(
        self, 
        alert_level: AlertLevel, 
        ffm_results: FFMResult
    ) -> str:
        """
        Construye mensaje personalizado basado en análisis
        """
        base_message = self.alert_messages[alert_level]['description']
        
        # Agregar detalles específicos
        details = []
        
        if ffm_results.failure_probability > 0.1:
            details.append(f"Probabilidad de erupción: {ffm_results.failure_probability:.1%}")
        
        if ffm_results.time_to_failure and ffm_results.time_to_failure < 72:
            details.append(f"Tiempo estimado: {ffm_results.time_to_failure:.1f} horas")
        
        if ffm_results.precursor_indicators:
            details.append(f"Precursores detectados: {len(ffm_results.precursor_indicators)}")
        
        if ffm_results.acceleration_trend != "ESTABLE":
            details.append(f"Tendencia: {ffm_results.acceleration_trend}")
        
        if details:
            return f"{base_message}. {' | '.join(details)}"
        else:
            return base_message
    
    def _generate_specific_recommendations(
        self, 
        alert_level: AlertLevel, 
        ffm_results: FFMResult
    ) -> List[str]:
        """
        Genera recomendaciones específicas basadas en análisis
        """
        base_recommendations = self.alert_messages[alert_level]['recommendations'].copy()
        
        # Agregar recomendaciones específicas según precursores
        if "CLUSTERING TEMPORAL ALTO" in ffm_results.precursor_indicators:
            base_recommendations.append("Monitorear swarms sísmicos continuamente")
        
        if "ACELERACIÓN SÍSMICA CRÍTICA" in ffm_results.precursor_indicators:
            base_recommendations.append("Verificar instrumentación sísmica")
        
        if "INTENSIDAD CRECIENTE" in ffm_results.precursor_indicators:
            base_recommendations.append("Evaluar deformación del terreno")
        
        if ffm_results.time_to_failure and ffm_results.time_to_failure < 12:
            base_recommendations.append("Activar evacuación inmediata")
        
        if ffm_results.confidence_level < 0.7:
            base_recommendations.append("Verificar calidad de datos sísmicos")
        
        return base_recommendations
    
    def _find_active_alert(self, alert_level: AlertLevel) -> Optional[AlertMessage]:
        """
        Busca una alerta activa del nivel especificado
        """
        for alert in self.active_alerts:
            if alert.level == alert_level and alert.active:
                return alert
        return None
    
    def _deactivate_lower_level_alerts(self, current_level: AlertLevel):
        """
        Desactiva alertas de nivel inferior
        """
        level_hierarchy = [AlertLevel.GREEN, AlertLevel.YELLOW, AlertLevel.ORANGE, AlertLevel.RED]
        current_index = level_hierarchy.index(current_level)
        
        for alert in self.active_alerts:
            if alert.active and level_hierarchy.index(alert.level) < current_index:
                alert.active = False
                logger.info(f"Alerta {alert.level.value} desactivada por escalada a {current_level.value}")
    
    def get_active_alerts(self) -> List[AlertMessage]:
        """
        Obtiene todas las alertas activas
        """
        return [alert for alert in self.active_alerts if alert.active]
    
    def get_alert_history(
        self, 
        hours: int = 24, 
        level: Optional[AlertLevel] = None
    ) -> List[AlertMessage]:
        """
        Obtiene historial de alertas
        
        Args:
            hours: Últimas N horas
            level: Filtrar por nivel específico
            
        Returns:
            Lista de alertas históricas
        """
        cutoff_time = datetime.utcnow() - timedelta(hours=hours)
        
        filtered_alerts = [
            alert for alert in self.alert_history
            if alert.timestamp >= cutoff_time
        ]
        
        if level:
            filtered_alerts = [
                alert for alert in filtered_alerts
                if alert.level == level
            ]
        
        return sorted(filtered_alerts, key=lambda x: x.timestamp, reverse=True)
    
    def clear_alerts(self):
        """
        Limpia todas las alertas (solo para testing)
        """
        self.active_alerts = []
        self.alert_history = []
        logger.info("Todas las alertas han sido limpiadas")
    
    def deactivate_alert(self, alert_id: str) -> bool:
        """
        Desactiva una alerta específica
        """
        for alert in self.active_alerts:
            if alert.id == alert_id:
                alert.active = False
                logger.info(f"Alerta {alert_id} desactivada manualmente")
                return True
        return False
    
    def get_alert_statistics(self) -> Dict[str, Any]:
        """
        Obtiene estadísticas de alertas
        """
        now = datetime.utcnow()
        last_24h = now - timedelta(hours=24)
        
        stats = {
            'total_alerts': len(self.alert_history),
            'active_alerts': len(self.get_active_alerts()),
            'last_24h': {
                'total': len([a for a in self.alert_history if a.timestamp >= last_24h]),
                'by_level': {}
            },
            'by_level': {}
        }
        
        # Contar por nivel
        for level in AlertLevel:
            stats['by_level'][level.value] = len([
                a for a in self.alert_history if a.level == level
            ])
            stats['last_24h']['by_level'][level.value] = len([
                a for a in self.alert_history 
                if a.level == level and a.timestamp >= last_24h
            ])
        
        return stats
    
    def update_thresholds(self, new_thresholds: Dict[str, Dict[str, float]]):
        """
        Actualiza los umbrales de alerta
        """
        for level_str, thresholds in new_thresholds.items():
            level = AlertLevel(level_str)
            if level in self.alert_thresholds:
                self.alert_thresholds[level].update(thresholds)
        
        logger.info(f"Umbrales de alerta actualizados: {self.alert_thresholds}")
    
    def export_alerts(self, format: str = "json") -> Dict[str, Any]:
        """
        Exporta alertas en formato específico
        """
        active_alerts = self.get_active_alerts()
        
        export_data = {
            'timestamp': datetime.utcnow().isoformat(),
            'active_alerts': [
                {
                    'id': alert.id,
                    'level': alert.level.value,
                    'message': alert.message,
                    'timestamp': alert.timestamp.isoformat(),
                    'probability': alert.probability,
                    'recommendations': alert.recommendations
                }
                for alert in active_alerts
            ],
            'statistics': self.get_alert_statistics()
        }
        
        return export_data