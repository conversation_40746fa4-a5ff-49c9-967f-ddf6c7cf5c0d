#!/usr/bin/env python3
"""
Script de diagnóstico para analizar problemas en el cálculo de derivadas
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import numpy as np
from datetime import datetime, timed<PERSON>ta
from core.data_generator import SeismicDataGenerator
from core.derivatives import DerivativeCalculator
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def analyze_normal_scenario():
    """Analiza un escenario normal para diagnosticar problemas"""
    print("=" * 60)
    print("DIAGNÓSTICO: ESCENARIO NORMAL")
    print("=" * 60)
    
    # Generar datos normales
    generator = SeismicDataGenerator(seed=42)  # Seed fijo para reproducibilidad
    normal_data = generator.generate_seismic_sequence(
        days=3, 
        eruption_scenario=False
    )
    
    print(f"Datos generados: {len(normal_data)} eventos")
    
    # Mostrar estadísticas de los datos originales
    magnitudes = [event.magnitude for event in normal_data]
    print(f"\nEstadísticas de magnitudes originales:")
    print(f"  Min: {min(magnitudes):.3f}")
    print(f"  Max: {max(magnitudes):.3f}")
    print(f"  Media: {np.mean(magnitudes):.3f}")
    print(f"  Std: {np.std(magnitudes):.3f}")
    print(f"  Rango: {max(magnitudes) - min(magnitudes):.3f}")
    
    # Calcular derivadas
    calculator = DerivativeCalculator()
    result = calculator.calculate_derivatives(normal_data, show_steps=False)
    
    # Analizar resultados
    print(f"\nResultados del cálculo:")
    print(f"  Primera derivada - Min: {min(result.first_derivative):.6f}, Max: {max(result.first_derivative):.6f}")
    print(f"  Segunda derivada - Min: {min(result.second_derivative):.6f}, Max: {max(result.second_derivative):.6f}")
    print(f"  Max aceleración: {result.max_acceleration:.6f}")
    print(f"  Tendencia: {result.acceleration_trend}")
    
    # Analizar estadísticas de segunda derivada
    second_deriv = np.array(result.second_derivative)
    mean_accel = np.mean(second_deriv)
    std_accel = np.std(second_deriv)
    max_accel = np.max(np.abs(second_deriv))
    
    print(f"\nAnálisis detallado de segunda derivada:")
    print(f"  Media: {mean_accel:.6f}")
    print(f"  Std: {std_accel:.6f}")
    print(f"  Max absoluto: {max_accel:.6f}")
    print(f"  Umbral 1σ: {std_accel:.6f}")
    print(f"  Umbral 2σ: {2 * std_accel:.6f}")
    print(f"  Umbral 3σ: {3 * std_accel:.6f}")
    
    # Verificar umbrales
    print(f"\nVerificación de umbrales:")
    print(f"  max_accel > 3σ: {max_accel > 3 * std_accel} ({max_accel:.6f} > {3 * std_accel:.6f})")
    print(f"  max_accel > 2σ: {max_accel > 2 * std_accel} ({max_accel:.6f} > {2 * std_accel:.6f})")
    print(f"  max_accel > 1σ: {max_accel > std_accel} ({max_accel:.6f} > {std_accel:.6f})")
    
    # Mostrar algunos valores de segunda derivada
    print(f"\nPrimeros 10 valores de segunda derivada:")
    for i, val in enumerate(result.second_derivative[:10]):
        print(f"  [{i}]: {val:.6f}")
    
    return result

def analyze_eruption_scenario():
    """Analiza un escenario pre-erupción para comparar"""
    print("\n" + "=" * 60)
    print("DIAGNÓSTICO: ESCENARIO PRE-ERUPCIÓN")
    print("=" * 60)
    
    # Generar datos pre-erupción
    generator = SeismicDataGenerator(seed=42)
    eruption_data = generator.generate_seismic_sequence(
        days=3, 
        eruption_scenario=True
    )
    
    print(f"Datos generados: {len(eruption_data)} eventos")
    
    # Mostrar estadísticas de los datos originales
    magnitudes = [event.magnitude for event in eruption_data]
    print(f"\nEstadísticas de magnitudes originales:")
    print(f"  Min: {min(magnitudes):.3f}")
    print(f"  Max: {max(magnitudes):.3f}")
    print(f"  Media: {np.mean(magnitudes):.3f}")
    print(f"  Std: {np.std(magnitudes):.3f}")
    print(f"  Rango: {max(magnitudes) - min(magnitudes):.3f}")
    
    # Calcular derivadas
    calculator = DerivativeCalculator()
    result = calculator.calculate_derivatives(eruption_data, show_steps=False)
    
    # Analizar resultados
    print(f"\nResultados del cálculo:")
    print(f"  Primera derivada - Min: {min(result.first_derivative):.6f}, Max: {max(result.first_derivative):.6f}")
    print(f"  Segunda derivada - Min: {min(result.second_derivative):.6f}, Max: {max(result.second_derivative):.6f}")
    print(f"  Max aceleración: {result.max_acceleration:.6f}")
    print(f"  Tendencia: {result.acceleration_trend}")
    
    return result

def test_small_dataset():
    """Prueba con un dataset pequeño y controlado"""
    print("\n" + "=" * 60)
    print("DIAGNÓSTICO: DATASET PEQUEÑO CONTROLADO")
    print("=" * 60)
    
    # Crear datos sintéticos muy simples
    from models.volcanic_data import SeismicReading
    
    base_time = datetime.utcnow()
    simple_data = [
        SeismicReading(
            timestamp=base_time + timedelta(hours=i),
            magnitude=2.0 + 0.1 * i,  # Incremento muy pequeño
            frequency=3.0,
            duration=5.0,
            depth=10.0
        )
        for i in range(10)
    ]
    
    print(f"Datos creados: {len(simple_data)} eventos")
    magnitudes = [event.magnitude for event in simple_data]
    print(f"Magnitudes: {[f'{m:.1f}' for m in magnitudes]}")
    
    # Calcular derivadas
    calculator = DerivativeCalculator()
    result = calculator.calculate_derivatives(simple_data, show_steps=False)
    
    print(f"\nResultados:")
    print(f"  Primera derivada: {[f'{d:.6f}' for d in result.first_derivative[:5]]}")
    print(f"  Segunda derivada: {[f'{d:.6f}' for d in result.second_derivative[:5]]}")
    print(f"  Max aceleración: {result.max_acceleration:.6f}")
    print(f"  Tendencia: {result.acceleration_trend}")
    
    return result

def main():
    """Función principal de diagnóstico"""
    print("INICIANDO DIAGNÓSTICO DEL SISTEMA DE DERIVADAS")
    print("=" * 60)
    
    try:
        # Analizar escenario normal
        normal_result = analyze_normal_scenario()
        
        # Analizar escenario pre-erupción
        eruption_result = analyze_eruption_scenario()
        
        # Probar con dataset controlado
        simple_result = test_small_dataset()
        
        # Resumen comparativo
        print("\n" + "=" * 60)
        print("RESUMEN COMPARATIVO")
        print("=" * 60)
        print(f"Normal    - Max aceleración: {normal_result.max_acceleration:.6f}, Tendencia: {normal_result.acceleration_trend}")
        print(f"Erupción  - Max aceleración: {eruption_result.max_acceleration:.6f}, Tendencia: {eruption_result.acceleration_trend}")
        print(f"Simple    - Max aceleración: {simple_result.max_acceleration:.6f}, Tendencia: {simple_result.acceleration_trend}")
        
        # Identificar problemas
        print("\n" + "=" * 60)
        print("PROBLEMAS IDENTIFICADOS")
        print("=" * 60)
        
        if normal_result.acceleration_trend != "ACTIVIDAD ESTABLE":
            print("❌ PROBLEMA: Actividad normal clasificada como aceleración")
            print(f"   Clasificación actual: {normal_result.acceleration_trend}")
            print(f"   Debería ser: ACTIVIDAD ESTABLE")
        else:
            print("✅ OK: Actividad normal clasificada correctamente")
            
        if eruption_result.acceleration_trend in ["ACTIVIDAD ESTABLE", "ACELERACIÓN LEVE"]:
            print("❌ PROBLEMA: Actividad pre-erupción no detectada correctamente")
            print(f"   Clasificación actual: {eruption_result.acceleration_trend}")
            print(f"   Debería ser: ACELERACIÓN CRÍTICA o MODERADA")
        else:
            print("✅ OK: Actividad pre-erupción detectada correctamente")
            
    except Exception as e:
        logger.error(f"Error durante el diagnóstico: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
