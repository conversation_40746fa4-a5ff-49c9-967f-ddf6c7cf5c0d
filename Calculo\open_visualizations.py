#!/usr/bin/env python3
"""
Script para abrir las visualizaciones sísmicas en el navegador
Permite acceder rápidamente a los diferentes dashboards generados
"""

import webbrowser
import os
from pathlib import Path

def open_visualizations():
    """Abre las visualizaciones en el navegador"""
    print("🌋 VISUALIZACIONES SÍSMICAS INTERACTIVAS")
    print("   Selecciona qué dashboard deseas ver:\n")
    
    # Rutas a los archivos HTML
    viz_dir = Path("visualizations")
    dashboards = {
        "1": ("seismic_dashboard.html", "Dashboard Principal"),
        "2": ("dashboard_normal.html", "Escenario Normal"),
        "3": ("dashboard_pre_eruption.html", "Escenario Pre-erupción"),
        "4": ("dashboard_critical.html", "Escenario Crítico"),
        "5": ("ALL", "Abrir todos los dashboards")
    }
    
    # Mostrar opciones
    for key, (filename, description) in dashboards.items():
        print(f"   {key}. {description}")
    
    print("\n   0. Salir")
    
    # Obtener selección del usuario
    choice = input("\n🎯 Selecciona una opción (1-5): ").strip()
    
    if choice == "0":
        print("   👋 ¡Hasta luego!")
        return
    
    if choice in dashboards:
        filename, description = dashboards[choice]
        
        if choice == "5":
            # Abrir todos los dashboards
            print("\n🚀 Abriendo todos los dashboards...")
            for i in range(1, 5):
                file_path = viz_dir / dashboards[str(i)][0]
                if file_path.exists():
                    abs_path = os.path.abspath(file_path)
                    webbrowser.open(f"file://{abs_path}")
                    print(f"   ✅ {dashboards[str(i)][1]}")
                else:
                    print(f"   ❌ No encontrado: {file_path}")
        else:
            # Abrir dashboard específico
            file_path = viz_dir / filename
            if file_path.exists():
                abs_path = os.path.abspath(file_path)
                webbrowser.open(f"file://{abs_path}")
                print(f"\n🚀 Abriendo: {description}")
                print(f"   📂 Archivo: {abs_path}")
            else:
                print(f"\n❌ Error: No se encontró el archivo {file_path}")
                print("   💡 Ejecuta 'python seismic_visualizer.py' para generar las visualizaciones")
    else:
        print("\n❌ Opción inválida. Selecciona un número del 1-5.")
        return open_visualizations()
    
    print("\n💡 Consejos para usar las visualizaciones:")
    print("   • Usa los botones para cambiar entre diferentes tipos de gráficos")
    print("   • Haz clic en 'Tiempo Real' para ver simulación en vivo")
    print("   • Pasa el cursor sobre los puntos para ver detalles")
    print("   • Los gráficos se adaptan automáticamente al tamaño de la ventana")

def show_visualization_info():
    """Muestra información sobre las visualizaciones disponibles"""
    print("\n📊 INFORMACIÓN DE LAS VISUALIZACIONES")
    print("="*50)
    
    info = {
        "Dashboard Principal": {
            "archivo": "seismic_dashboard.html",
            "descripción": "Datos sísmicos completos con escenario de pre-erupción",
            "características": [
                "Gráfico de magnitud vs tiempo",
                "Análisis de derivadas",
                "Distribución de frecuencias",
                "Modo tiempo real",
                "Alertas y estadísticas"
            ]
        },
        "Escenario Normal": {
            "archivo": "dashboard_normal.html",
            "descripción": "Actividad sísmica normal, dentro de parámetros típicos",
            "características": [
                "Nivel de alerta: VERDE",
                "Magnitudes bajas (< 3.0)",
                "Actividad estable",
                "Ideal para entrenamiento"
            ]
        },
        "Escenario Pre-erupción": {
            "archivo": "dashboard_pre_eruption.html",
            "descripción": "Actividad sísmica incrementada con signos precursores",
            "características": [
                "Nivel de alerta: NARANJA/ROJO",
                "Magnitudes crecientes",
                "Patrones de aceleración",
                "Indicadores de riesgo"
            ]
        },
        "Escenario Crítico": {
            "archivo": "dashboard_critical.html",
            "descripción": "Actividad sísmica crítica con alta probabilidad de erupción",
            "características": [
                "Nivel de alerta: ROJO",
                "Magnitudes altas (> 4.0)",
                "Aceleración crítica",
                "Recomendaciones de evacuación"
            ]
        }
    }
    
    for dashboard, details in info.items():
        print(f"\n🌋 {dashboard}")
        print(f"   📄 Archivo: {details['archivo']}")
        print(f"   📝 Descripción: {details['descripción']}")
        print(f"   🔧 Características:")
        for caracteristica in details['características']:
            print(f"      • {caracteristica}")
    
    print("\n🎯 CÓMO INTERPRETAR LOS GRÁFICOS:")
    print("   📈 Magnitud vs Tiempo:")
    print("      • Línea ascendente = Incremento en actividad")
    print("      • Puntos rojos = Eventos significativos (mag > 4.0)")
    print("      • Puntos verdes = Eventos menores (mag < 4.0)")
    print("   ")
    print("   📊 Análisis de Derivadas:")
    print("      • Valores positivos = Aceleración en actividad")
    print("      • Valores negativos = Desaceleración en actividad")
    print("      • Cruces por cero = Cambios de tendencia")
    print("   ")
    print("   📋 Distribución de Frecuencia:")
    print("      • Barras altas = Magnitudes más comunes")
    print("      • Distribución normal = Actividad típica")
    print("      • Asimetría = Posibles anomalías")

def main():
    """Función principal"""
    print("🎨 SISTEMA DE VISUALIZACIONES SÍSMICAS")
    print("   Acceso rápido a dashboards interactivos")
    print("   Desarrollado para monitoreo volcánico\n")
    
    while True:
        print("\n🔥 MENÚ PRINCIPAL")
        print("   1. Abrir visualizaciones")
        print("   2. Información sobre dashboards")
        print("   3. Generar nuevas visualizaciones")
        print("   0. Salir")
        
        choice = input("\n🎯 Selecciona una opción: ").strip()
        
        if choice == "1":
            open_visualizations()
        elif choice == "2":
            show_visualization_info()
        elif choice == "3":
            print("\n🎨 Generando nuevas visualizaciones...")
            try:
                import subprocess
                result = subprocess.run(["python", "seismic_visualizer.py"], 
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    print("   ✅ Visualizaciones generadas exitosamente")
                else:
                    print(f"   ❌ Error: {result.stderr}")
            except Exception as e:
                print(f"   ❌ Error al generar visualizaciones: {e}")
        elif choice == "0":
            print("\n👋 ¡Gracias por usar el sistema de visualizaciones!")
            break
        else:
            print("\n❌ Opción inválida. Selecciona 0-3.")

if __name__ == "__main__":
    main()
