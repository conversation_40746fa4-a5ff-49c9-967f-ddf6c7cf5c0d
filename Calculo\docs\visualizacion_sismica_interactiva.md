# Sistema de Visualización Sísmica Interactiva

## Descripción General

Este documento describe el sistema de visualización interactiva de datos sísmicos desarrollado para el sistema de predicción volcánica. El sistema genera dashboards web interactivos usando HTML, CSS y JavaScript con la biblioteca D3.js para crear gráficos dinámicos que muestran la actividad sísmica en tiempo real.

## ¿Qué es la Escala de Richter?

La **E<PERSON><PERSON> de <PERSON>** es una escala logarítmica que mide la magnitud de los terremotos. Fue desarrollada por Charles <PERSON> en 1935 y es fundamental para entender nuestras visualizaciones:

### Características de la Escala:
- **Escala logarítmica en base 10**: Cada unidad representa un aumento de 10 veces en la amplitud
- **Ejemplo**: Un terremoto de magnitud 5.0 es 10 veces más fuerte que uno de 4.0
- **Rango típic<PERSON>**: De 0 a 10+ (aunque teóricamente no tiene límite superior)

### Interpretación de Magnitudes:
- **1.0 - 2.9**: Micro-terremotos, generalmente no se sienten
- **3.0 - 3.9**: Terremotos menores, apenas perceptibles
- **4.0 - 4.9**: Terremotos ligeros, se sienten claramente
- **5.0 - 5.9**: Terremotos moderados, pueden causar daños menores
- **6.0 - 6.9**: Terremotos fuertes, pueden causar daños significativos
- **7.0+**: Terremotos mayores, potencialmente destructivos

## Representación en Coordenadas Cartesianas

### ¿Es Posible Graficar en Ejes Cartesianos?

**Sí, absolutamente.** Los valores de la escala de Richter se pueden representar directamente en coordenadas cartesianas:

- **Eje X**: Tiempo (timestamps de los eventos sísmicos)
- **Eje Y**: Magnitud (valores de la escala de Richter: 1.5, 3.2, 4.8, etc.)

### Ventajas de la Representación Cartesiana:
1. **Visualización clara** de tendencias temporales
2. **Fácil identificación** de patrones y anomalías
3. **Comparación directa** entre diferentes períodos
4. **Análisis de derivadas** para detectar aceleraciones

## Estructura del Sistema

### Archivos Principales

#### 1. `seismic_visualizer.py`
**Función**: Generador principal del sistema de visualización
**Características**:
- Clase `SeismicVisualizer` que procesa datos sísmicos
- Generación de múltiples tipos de gráficos
- Integración con el sistema de predicción volcánica existente

#### 2. Archivos HTML Generados
- `seismic_dashboard.html`: Dashboard principal
- `dashboard_normal.html`: Escenario de actividad normal
- `dashboard_pre_eruption.html`: Escenario pre-erupción
- `dashboard_critical.html`: Escenario crítico

### Tipos de Visualizaciones

#### 1. Gráfico de Magnitud vs Tiempo
**Descripción**: Línea temporal que muestra la evolución de las magnitudes sísmicas
**Características**:
- **Eje X**: Tiempo (formato HH:MM)
- **Eje Y**: Magnitud (escala de Richter)
- **Puntos coloreados**: Verde para magnitudes < 4.0, Rojo para magnitudes ≥ 4.0
- **Interactividad**: Tooltips con información detallada

#### 2. Análisis de Derivadas
**Descripción**: Muestra la primera derivada (velocidad de cambio) de la actividad sísmica
**Propósito**: Detectar aceleraciones y desaceleraciones en la actividad
**Características**:
- **Línea de referencia** en y=0
- **Puntos críticos** resaltados cuando |derivada| > 0.5
- **Interpretación**: Valores positivos = aceleración, valores negativos = desaceleración

#### 3. Distribución de Frecuencia
**Descripción**: Histograma que muestra la distribución de magnitudes
**Utilidad**: Identificar los rangos de magnitud más comunes
**Características**:
- **Barras verdes** representan la frecuencia de cada rango
- **10 bins** para distribución óptima

#### 4. Patrones Sísmicos
**Descripción**: Análisis de patrones específicos en la actividad sísmica
**Tipos de patrones**:
- Aumento lineal
- Aumento exponencial
- Patrones periódicos
- Ráfagas aleatorias

## Características Técnicas

### Tecnologías Utilizadas

#### Frontend
- **HTML5**: Estructura semántica y moderna
- **CSS3**: Estilos avanzados con gradientes, transparencias y animaciones
- **JavaScript ES6+**: Lógica de interactividad y manipulación del DOM
- **D3.js v7**: Biblioteca para visualizaciones de datos

#### Backend
- **Python 3.8+**: Generación y procesamiento de datos
- **Pydantic**: Validación y modelado de datos
- **FastAPI**: (integración con el sistema existente)

### Características de Diseño

#### Diseño Responsivo
- **Grid CSS**: Adaptación automática a diferentes tamaños de pantalla
- **Media queries**: Optimización para dispositivos móviles
- **Flexbox**: Distribución flexible de controles

#### Efectos Visuales
- **Backdrop blur**: Efectos de desenfoque en el fondo
- **Gradientes**: Fondos degradados para mejor estética
- **Sombras**: Profundidad y separación visual
- **Transiciones**: Animaciones suaves en interacciones

### Funcionalidades Interactivas

#### Controles de Navegación
- **Botones de tipo de gráfico**: Cambio dinámico entre visualizaciones
- **Modo tiempo real**: Simulación de datos en vivo
- **Tooltips informativos**: Información detallada al pasar el cursor

#### Actualización de Datos
- **Tiempo real**: Nuevos datos cada 2 segundos en modo simulación
- **Limite de datos**: Mantiene solo los últimos 100 puntos para rendimiento
- **Estadísticas dinámicas**: Actualización automática de métricas

## Panel de Estadísticas

### Métricas Principales

#### 1. Eventos Totales
**Descripción**: Número total de eventos sísmicos en las últimas 24 horas
**Fuente**: Conteo directo de registros sísmicos

#### 2. Magnitud Máxima
**Descripción**: La magnitud más alta registrada
**Unidad**: Escala de Richter (ejemplo: 5.34)

#### 3. Probabilidad de Erupción
**Descripción**: Probabilidad calculada por el modelo FFM
**Formato**: Porcentaje (ejemplo: 82.2%)

#### 4. Aceleración Máxima
**Descripción**: Valor máximo de la segunda derivada
**Interpretación**: Indica la rapidez del cambio en la actividad

## Sistema de Alertas

### Niveles de Alerta

#### 🟢 VERDE (Normal)
- **Actividad**: Dentro de parámetros normales
- **Acción**: Monitoreo rutinario
- **Probabilidad**: < 25%

#### 🟡 AMARILLO (Precaución)
- **Actividad**: Ligero incremento detectado
- **Acción**: Monitoreo intensificado
- **Probabilidad**: 25% - 50%

#### 🟠 NARANJA (Alerta)
- **Actividad**: Incremento significativo
- **Acción**: Preparación de protocolos
- **Probabilidad**: 50% - 75%

#### 🔴 ROJO (Crítico)
- **Actividad**: Actividad crítica detectada
- **Acción**: Activación de protocolos de emergencia
- **Probabilidad**: > 75%

## Uso del Sistema

### Instalación y Configuración

#### Requisitos Previos
```bash
# Python 3.8+
# Dependencias del proyecto volcanoApp
```

#### Ejecución
```bash
# Navegar al directorio del proyecto
cd volcanoApp/Calculo

# Ejecutar el generador de visualizaciones
uv run python seismic_visualizer.py
```

### Acceso a las Visualizaciones

#### Método 1: Apertura Automática
El sistema abre automáticamente el dashboard principal en el navegador predeterminado.

#### Método 2: Apertura Manual
1. Navegar a la carpeta `visualizations/`
2. Abrir cualquier archivo `.html` en un navegador web
3. Usar los controles para explorar diferentes visualizaciones

### Interpretación de Datos

#### Lectura de Gráficos

##### Gráfico de Magnitud vs Tiempo
- **Línea ascendente**: Incremento en la actividad sísmica
- **Picos altos**: Eventos sísmicos significativos
- **Tendencia general**: Patrón de actividad volcánica

##### Gráfico de Derivadas
- **Valores positivos**: Aceleración en la actividad
- **Valores negativos**: Desaceleración en la actividad
- **Cruces por cero**: Cambios en la tendencia

##### Distribución de Frecuencia
- **Barras altas**: Magnitudes más comunes
- **Distribución normal**: Actividad típica
- **Asimetría**: Posibles anomalías

## Integración con el Sistema Existente

### Fuentes de Datos

#### Modelos de Datos
- **SeismicReading**: Lecturas sísmicas individuales
- **DerivativeResult**: Resultados de cálculos de derivadas
- **FFMResult**: Resultados del modelo FFM
- **AlertLevel**: Niveles de alerta del sistema

#### Generadores de Datos
- **SeismicDataGenerator**: Generación de datos sintéticos
- **DerivativeCalculator**: Cálculos de derivadas
- **FFMModel**: Modelo de predicción de fallas
- **AlertSystem**: Sistema de alertas tempranas

### Escenarios de Uso

#### 1. Monitoreo Normal
**Objetivo**: Vigilancia rutinaria de la actividad volcánica
**Datos**: Actividad sísmica de los últimos 2-3 días
**Frecuencia**: Actualización cada hora

#### 2. Pre-erupción
**Objetivo**: Detección temprana de signos precursores
**Datos**: Actividad intensificada con incrementos graduales
**Frecuencia**: Actualización cada 15 minutos

#### 3. Crítico
**Objetivo**: Monitoreo de emergencia
**Datos**: Actividad crítica con eventos intensos
**Frecuencia**: Actualización cada 2 minutos

## Mantenimiento y Extensión

### Personalización

#### Modificación de Estilos
Los estilos CSS están embebidos en el HTML para facilitar la portabilidad. Para modificar:
1. Editar la sección `<style>` en `seismic_visualizer.py`
2. Regenerar las visualizaciones

#### Nuevos Tipos de Gráficos
Para agregar nuevas visualizaciones:
1. Crear una nueva función `drawNuevoGrafico()` en JavaScript
2. Agregar un botón de control correspondiente
3. Implementar la lógica de cambio en `showChart()`

#### Datos Adicionales
Para incorporar nuevos tipos de datos:
1. Modificar el método `prepare_data_for_visualization()`
2. Actualizar los modelos de datos si es necesario
3. Agregar el procesamiento en JavaScript

### Resolución de Problemas

#### Problemas Comunes

##### 1. Gráficos No Se Muestran
**Causa**: Error en la carga de D3.js
**Solución**: Verificar conexión a internet o usar D3.js local

##### 2. Datos No Se Actualizan
**Causa**: Error en la generación de datos
**Solución**: Verificar que los modelos de datos estén correctos

##### 3. Estilos No Se Aplican
**Causa**: Conflictos CSS
**Solución**: Verificar la especificidad de los selectores

### Optimización de Rendimiento

#### Recomendaciones
1. **Limitar datos**: Mantener un máximo de 100-200 puntos por gráfico
2. **Debouncing**: Aplicar retrasos en actualizaciones frecuentes
3. **Lazy loading**: Cargar gráficos solo cuando sea necesario
4. **Compresión**: Comprimir archivos HTML para deployment

## Casos de Uso Avanzados

### Análisis Predictivo

#### Detección de Patrones
El sistema puede identificar:
- **Tendencias lineales**: Incrementos constantes
- **Patrones exponenciales**: Aceleraciones críticas
- **Ciclos periódicos**: Actividad cíclica
- **Anomalías**: Eventos fuera de lo normal

#### Alertas Tempranas
Combinando visualizaciones con el sistema FFM:
- **Predicción de tiempo hasta falla**: Estimación de erupciones
- **Confianza del modelo**: Nivel de certeza en predicciones
- **Indicadores precursores**: Señales de alerta temprana

### Integración con Sistemas Externos

#### APIs REST
Las visualizaciones pueden integrarse con:
- **Sistemas de monitoreo**: Datos en tiempo real
- **Bases de datos**: Históricos de actividad
- **Servicios de notificación**: Alertas automáticas

#### Exportación de Datos
Los gráficos pueden exportarse como:
- **Imágenes PNG/SVG**: Para reportes
- **Datos JSON**: Para análisis externos
- **Datos CSV**: Para procesamiento estadístico

## Conclusión

Este sistema de visualización proporciona una herramienta poderosa e intuitiva para el monitoreo y análisis de la actividad sísmica volcánica. La combinación de tecnologías web modernas con algoritmos de procesamiento de datos permite:

1. **Monitoreo en tiempo real** de la actividad volcánica
2. **Análisis predictivo** basado en modelos matemáticos
3. **Alertas tempranas** para proteger comunidades
4. **Interfaz intuitiva** para operadores no técnicos

El sistema está diseñado para ser **extensible**, **mantenible** y **escalable**, permitiendo futuras mejoras y adaptaciones según las necesidades específicas del monitoreo volcánico.

---

## Información Técnica Adicional

### Archivos Generados
- `seismic_dashboard.html`: Dashboard principal
- `dashboard_normal.html`: Escenario normal
- `dashboard_pre_eruption.html`: Escenario pre-erupción  
- `dashboard_critical.html`: Escenario crítico

### Dependencias
- D3.js v7 (CDN)
- Navegador web moderno con soporte para ES6+
- Python 3.8+ para generación de datos

### Autor
Sistema desarrollado como parte del proyecto volcanoApp para análisis y predicción de actividad volcánica.

### Última Actualización
Julio 2025 - Versión 1.0
