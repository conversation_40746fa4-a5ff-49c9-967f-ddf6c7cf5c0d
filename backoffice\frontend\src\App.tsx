/**
 * 🌋 Volcano App Frontend - Panel Administrativo Moderno con shadcn/ui
 * Sistema completo de gestión de alertas volcánicas y zonas de seguridad
 */

import axios from 'axios';
import React, { useEffect, useState } from 'react';
import { AlertDialog } from './components/alerts/AlertDialog';
import { AlertsTable } from './components/alerts/AlertsTable';
import { DashboardStats } from './components/dashboard/DashboardStats';
import { DebugPanel } from './components/debug/DebugPanel';
import { InteractiveMapPage } from './components/InteractiveMapPage';
import { AppLayout } from './components/layout/AppLayout';
import { Toaster } from './components/ui/toaster';
import { ZoneModal } from './components/ZoneModal';
import { ZonesTable } from './components/zones/ZonesTable';
import { useToast } from './hooks/use-toast';
import { debug } from './utils/debug';
import { CalculoVolcanico } from './components/calculo/CalculoVolcanico';

// Configuración del API
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001';

// Cliente HTTP
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Tipos
interface Alert {
  id: string;
  title: string;
  message: string;
  alert_level: 'NORMAL' | 'ADVISORY' | 'WATCH' | 'WARNING' | 'EMERGENCY';
  volcano_name: string;
  volcano_lat: number;
  volcano_lng: number;
  is_active: boolean;
  is_scheduled: boolean;
  scheduled_for?: string;
  expires_at?: string;
  created_by?: string;
  created_at: string;
  updated_at: string;
  metadata: any;
}

interface Zone {
  id: string;
  name: string;
  description: string;
  zone_type: 'SAFE' | 'EMERGENCY' | 'EVACUATION' | 'DANGER';
  geometry: {
    type: string;
    coordinates: number[][][];
  };
  capacity?: number;
  contact_info: any;
  facilities: any;
  is_active: boolean;
  version: number;
  created_by?: string;
  created_at: string;
  updated_at: string;
  metadata: any;
}

interface AuditLog {
  id: string;
  user_id: string;
  action: string;
  table_name: string;
  record_id: string;
  old_values: any;
  new_values: any;
  ip_address: string;
  user_agent: string;
  created_at: string;
}

interface HealthStatus {
  status: string;
  services: {
    database: boolean;
    server: boolean;
  };
  timestamp: string;
}

interface AlertFormData {
  title: string;
  message: string;
  alert_level: string;
  volcano_name: string;
  volcano_lat: number;
  volcano_lng: number;
  is_active: boolean;
  expires_at?: string;
}

function App() {
  // Hook para toast notifications
  const { toast } = useToast();

  // Estados principales
  const [health, setHealth] = useState<HealthStatus | null>(null);
  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [zones, setZones] = useState<Zone[]>([]);
  const [auditLogs, setAuditLogs] = useState<AuditLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<string>('dashboard');

  // Estados para modales y formularios
  const [showAlertDialog, setShowAlertDialog] = useState(false);
  const [showZoneModal, setShowZoneModal] = useState(false);
  const [editingAlert, setEditingAlert] = useState<Alert | null>(null);
  const [editingZone, setEditingZone] = useState<Zone | null>(null);
  const [isCreatingZone, setIsCreatingZone] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Estado para panel de debugging
  const [showDebugPanel, setShowDebugPanel] = useState(false);

  // Funciones para manejo de zonas
  const handleZoneCreated = async (newZone: Zone) => {
    setZones(prevZones => [...prevZones, newZone]);
    setShowZoneModal(false);
    setIsCreatingZone(false);
    setEditingZone(null);
    toast({
      title: "Éxito",
      description: "Zona creada correctamente"
    });
    // Refrescar datos
    await fetchZones();
  };

  const handleZoneUpdated = async (updatedZone: Zone) => {
    setZones(prevZones =>
      prevZones.map(zone =>
        zone.id === updatedZone.id ? updatedZone : zone
      )
    );
    setShowZoneModal(false);
    setIsCreatingZone(false);
    setEditingZone(null);
    toast({
      title: "Éxito",
      description: "Zona actualizada correctamente"
    });
    // Refrescar datos
    await fetchZones();
  };

  // Funciones de API
  const fetchHealth = async () => {
    try {
      const response = await apiClient.get('/health');
      setHealth(response.data);
    } catch (err) {
      console.error('Error fetching health:', err);
      toast({
        variant: "destructive",
        title: "Error de conexión",
        description: "No se pudo conectar con el backend"
      });
    }
  };

  const fetchAlerts = async () => {
    try {
      const response = await apiClient.get('/api/alerts');
      setAlerts(response.data.data || []);
    } catch (err) {
      console.error('Error fetching alerts:', err);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Error al cargar alertas"
      });
    }
  };

  const fetchZones = async () => {
    try {
      const response = await apiClient.get('/api/zones');
      setZones(response.data.data || []);
    } catch (err) {
      console.error('Error fetching zones:', err);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Error al cargar zonas"
      });
    }
  };

  const fetchAuditLogs = async () => {
    try {
      const response = await apiClient.get('/api/audit?limit=50');
      setAuditLogs(response.data.data || []);
    } catch (err) {
      console.error('Error fetching audit logs:', err);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Error al cargar logs de auditoría"
      });
    }
  };

  // Cargar datos al iniciar
  useEffect(() => {
    const loadData = async () => {
      debug.info('Iniciando carga de datos de la aplicación', {}, 'general');
      setLoading(true);
      try {
        await Promise.all([
          fetchHealth(),
          fetchAlerts(),
          fetchZones(),
          fetchAuditLogs()
        ]);
        debug.info('Datos cargados exitosamente', {}, 'general');
      } catch (error) {
        debug.error('Error al cargar datos iniciales', error, 'general');
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  // Atajo de teclado para abrir panel de debugging (Ctrl+Shift+D)
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.ctrlKey && event.shiftKey && event.key === 'D') {
        event.preventDefault();
        setShowDebugPanel(prev => !prev);
        debug.info('Panel de debugging toggled via keyboard shortcut', {}, 'ui');
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);

  // Función para refrescar datos
  const refreshData = async () => {
    setLoading(true);
    await Promise.all([
      fetchHealth(),
      fetchAlerts(),
      fetchZones(),
      fetchAuditLogs()
    ]);
    setLoading(false);
    toast({
      title: "Éxito",
      description: "Datos actualizados"
    });
  };

  // CRUD para alertas
  const createAlert = async (alertData: AlertFormData) => {
    setIsSubmitting(true);
    try {
      await apiClient.post('/api/alerts', alertData);
      toast({
        title: "Éxito",
        description: "Alerta creada exitosamente"
      });
      setShowAlertDialog(false);
      await fetchAlerts();
      await fetchAuditLogs();
    } catch (err: any) {
      console.error('Error creating alert:', err);
      toast({
        variant: "destructive",
        title: "Error",
        description: err.response?.data?.error || 'Error al crear alerta'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const updateAlert = async (id: string, alertData: Partial<AlertFormData>) => {
    setIsSubmitting(true);
    try {
      await apiClient.put(`/api/alerts/${id}`, alertData);
      toast({
        title: "Éxito",
        description: "Alerta actualizada exitosamente"
      });
      setShowAlertDialog(false);
      setEditingAlert(null);
      await fetchAlerts();
      await fetchAuditLogs();
    } catch (err: any) {
      console.error('Error updating alert:', err);
      toast({
        variant: "destructive",
        title: "Error",
        description: err.response?.data?.error || 'Error al actualizar alerta'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const deleteAlert = async (id: string) => {
    if (!confirm('¿Estás seguro de que quieres eliminar esta alerta?')) return;

    try {
      await apiClient.delete(`/api/alerts/${id}`);
      toast({
        title: "Éxito",
        description: "Alerta eliminada exitosamente"
      });
      await fetchAlerts();
      await fetchAuditLogs();
    } catch (err: any) {
      console.error('Error deleting alert:', err);
      toast({
        variant: "destructive",
        title: "Error",
        description: err.response?.data?.error || 'Error al eliminar alerta'
      });
    }
  };

  // Funciones de manejo de modales
  const openCreateAlertDialog = () => {
    setEditingAlert(null);
    setShowAlertDialog(true);
  };

  const openEditAlertDialog = (alert: Alert) => {
    setEditingAlert(alert);
    setShowAlertDialog(true);
  };

  const handleAlertSave = (id: string | null, data: AlertFormData) => {
    if (id) {
      updateAlert(id, data);
    } else {
      createAlert(data);
    }
  };

  // Renderizar contenido según la pestaña activa
  const renderContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return (
          <DashboardStats
            health={health}
            alerts={alerts}
            zones={zones}
            auditLogs={auditLogs}
          />
        );
      case 'alerts':
        return (
          <AlertsTable
            alerts={alerts}
            onCreateAlert={openCreateAlertDialog}
            onEditAlert={openEditAlertDialog}
            onDeleteAlert={deleteAlert}
          />
        );
      case 'zones':
        return (
          <ZonesTable
            zones={zones}
            onCreateZone={() => {
              console.log('🔍 Botón Nueva Zona clickeado');
              setEditingZone(null);
              setIsCreatingZone(true);
              setShowZoneModal(true);
            }}
            onEditZone={(zone: Zone) => {
              setEditingZone(zone);
              setIsCreatingZone(false);
              setShowZoneModal(true);
            }}
            onDeleteZone={(id: string) => {
              console.log('Delete zone:', id);
              toast({
                title: "Información",
                description: "Función de eliminar zona en desarrollo"
              });
            }}
          />
        );
      case 'map':
        return (
          <InteractiveMapPage
            zones={zones}
            alerts={alerts}
            onRefresh={refreshData}
          />
        );
      case 'calculo':
        return <CalculoVolcanico />;
      case 'analytics':
        return <div className="p-6 text-center text-muted-foreground">Analíticas (En desarrollo)</div>;
      case 'users':
        return <div className="p-6 text-center text-muted-foreground">Gestión de Usuarios (En desarrollo)</div>;
      case 'audit':
        return <div className="p-6 text-center text-muted-foreground">Logs de Auditoría (En desarrollo)</div>;
      case 'settings':
        return <div className="p-6 text-center text-muted-foreground">Configuración (En desarrollo)</div>;
      default:
        return (
          <DashboardStats
            health={health}
            alerts={alerts}
            zones={zones}
            auditLogs={auditLogs}
          />
        );
    }
  };

  if (loading && alerts.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center p-8 bg-white rounded-lg shadow-lg">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 font-medium">Cargando Volcano App Admin...</p>
          <p className="text-sm text-gray-500 mt-2">Verificando conexión con el servidor...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <AppLayout
        activeTab={activeTab}
        onTabChange={setActiveTab}
        health={health}
        loading={loading}
        onRefresh={refreshData}
      >
        {renderContent()}
      </AppLayout>

      {/* Modales */}
      <AlertDialog
        open={showAlertDialog}
        onClose={() => {
          setShowAlertDialog(false);
          setEditingAlert(null);
        }}
        alert={editingAlert}
        onSave={handleAlertSave}
        isSubmitting={isSubmitting}
      />

      <ZoneModal
        isOpen={showZoneModal}
        onClose={() => {
          setShowZoneModal(false);
          setEditingZone(null);
          setIsCreatingZone(false);
        }}
        zone={editingZone}
        isCreating={isCreatingZone}
        onZoneCreated={handleZoneCreated}
        onZoneUpdated={handleZoneUpdated}
      />

      {/* Panel de Debugging */}
      <DebugPanel
        isOpen={showDebugPanel}
        onClose={() => setShowDebugPanel(false)}
      />

      {/* Toast Notifications */}
      <Toaster />
    </div>
  );
}

export default App;
