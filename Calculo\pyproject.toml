[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "volcanapp-calculo"
version = "1.0.0"
description = "Sistema de Predicción Volcánica - Monolito de Cálculo"
authors = [
    {name = "VolcanApp Team", email = "<EMAIL>"},
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.9"
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Scientific/Engineering :: Mathematics",
    "Topic :: Scientific/Engineering :: Physics",
]
keywords = ["volcano", "prediction", "seismic", "derivatives", "ffm"]

dependencies = [
    "fastapi>=0.104.1",
    "uvicorn>=0.24.0",
    "pydantic>=2.8.0",
    "pydantic-settings>=2.1.0",
    "python-multipart>=0.0.6",
    "httpx>=0.25.2",
    "requests>=2.32.4",
]

[project.optional-dependencies]
scientific = [
    "numpy>=1.26.0",
    "scipy>=1.12.0", 
    "matplotlib>=3.8.0",
    "pandas>=2.1.0",
    "sympy>=1.12",
]
dev = [
    "pytest",
    "pytest-asyncio",
    "pytest-cov",
    "black",
    "flake8",
    "mypy",
]

[project.scripts]
volcanapp-server = "main:main"
volcanapp-example = "run_example:main"

[project.urls]
Homepage = "https://github.com/volcanapp/volcanapp"
Documentation = "https://volcanapp.readthedocs.io"
Repository = "https://github.com/volcanapp/volcanapp.git"
Issues = "https://github.com/volcanapp/volcanapp/issues"

[tool.hatch.build.targets.wheel]
packages = ["core", "models"]
include = ["main.py", "run_example.py"]

[tool.uv]
dev-dependencies = [
    "pytest>=7.4.3",
    "pytest-asyncio>=0.21.1",
    "pytest-cov>=4.0.0",
    "black>=23.0.0",
    "flake8>=6.0.0",
    "mypy>=1.5.0",
]

[tool.black]
line-length = 88
target-version = ['py39']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --tb=short"
asyncio_mode = "auto"

[tool.coverage.run]
source = ["core", "models"]
omit = ["tests/*", "*/tests/*"]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
]
