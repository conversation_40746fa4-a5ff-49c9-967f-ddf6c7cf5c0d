"""
Generador de datos sísmicos ficticios para testing y demostración
Crea secuencias realistas de actividad sísmica volcánica
"""

import numpy as np
import random
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import logging

from models.volcanic_data import SeismicReading

logger = logging.getLogger(__name__)

class SeismicDataGenerator:
    """
    Generador de datos sísmicos sintéticos para volcanes
    """
    
    def __init__(self, seed: Optional[int] = None):
        if seed:
            np.random.seed(seed)
            random.seed(seed)
        
        # Parámetros base para generación
        self.base_parameters = {
            'normal_magnitude_range': (1.0, 3.0),
            'normal_frequency_range': (0.5, 5.0),
            'normal_duration_range': (0.5, 10.0),
            'normal_depth_range': (2.0, 15.0),
            'normal_event_rate': 0.3,  # eventos por hora
        }
        
        # Parámetros para escenario pre-erupción
        self.eruption_parameters = {
            'escalation_magnitude_range': (2.0, 5.5),
            'escalation_frequency_range': (1.0, 15.0),
            'escalation_duration_range': (1.0, 30.0),
            'escalation_depth_range': (1.0, 8.0),
            'escalation_event_rate': 2.0,  # eventos por hora
            'acceleration_factor': 1.5,
        }
    
    def generate_seismic_sequence(
        self,
        days: int = 7,
        eruption_scenario: bool = False,
        start_time: Optional[datetime] = None
    ) -> List[SeismicReading]:
        """
        Genera una secuencia de datos sísmicos
        
        Args:
            days: Duración en días
            eruption_scenario: Si simular escenario pre-erupción
            start_time: Tiempo de inicio (por defecto ahora)
            
        Returns:
            Lista de lecturas sísmicas
        """
        if start_time is None:
            start_time = datetime.utcnow() - timedelta(days=days)
        
        logger.info(f"Generando secuencia sísmica: {days} días, "
                   f"escenario_erupción={eruption_scenario}")
        
        seismic_data = []
        
        if eruption_scenario:
            seismic_data = self._generate_eruption_scenario(start_time, days)
        else:
            seismic_data = self._generate_normal_scenario(start_time, days)
        
        # Ordenar por timestamp
        seismic_data.sort(key=lambda x: x.timestamp)
        
        logger.info(f"Secuencia generada: {len(seismic_data)} eventos")
        return seismic_data
    
    def _generate_normal_scenario(
        self, 
        start_time: datetime, 
        days: int
    ) -> List[SeismicReading]:
        """
        Genera actividad sísmica normal
        """
        seismic_data = []
        current_time = start_time
        end_time = start_time + timedelta(days=days)
        
        while current_time < end_time:
            # Determinar si ocurre un evento
            if random.random() < self.base_parameters['normal_event_rate']:
                # Generar evento sísmico normal
                event = self._generate_normal_event(current_time)
                seismic_data.append(event)
            
            # Avanzar tiempo (simulación por horas)
            current_time += timedelta(hours=1)
        
        return seismic_data
    
    def _generate_eruption_scenario(
        self, 
        start_time: datetime, 
        days: int
    ) -> List[SeismicReading]:
        """
        Genera escenario pre-erupción con aceleración de precursores
        """
        seismic_data = []
        current_time = start_time
        end_time = start_time + timedelta(days=days)
        
        # Dividir en fases
        total_hours = days * 24
        phase1_hours = int(total_hours * 0.6)  # 60% actividad normal
        phase2_hours = int(total_hours * 0.3)  # 30% escalada
        phase3_hours = total_hours - phase1_hours - phase2_hours  # 10% pre-erupción
        
        # Fase 1: Actividad normal
        phase1_end = start_time + timedelta(hours=phase1_hours)
        while current_time < phase1_end:
            if random.random() < self.base_parameters['normal_event_rate']:
                event = self._generate_normal_event(current_time)
                seismic_data.append(event)
            current_time += timedelta(hours=1)
        
        # Fase 2: Escalada gradual
        phase2_end = phase1_end + timedelta(hours=phase2_hours)
        escalation_factor = 1.0
        while current_time < phase2_end:
            # Incrementar gradualmente la actividad
            progress = (current_time - phase1_end).total_seconds() / (phase2_hours * 3600)
            escalation_factor = 1.0 + progress * 2.0  # Hasta 3x más activo
            
            event_rate = self.base_parameters['normal_event_rate'] * escalation_factor
            if random.random() < min(event_rate, 1.0):
                event = self._generate_escalated_event(current_time, escalation_factor)
                seismic_data.append(event)
            
            current_time += timedelta(hours=1)
        
        # Fase 3: Pre-erupción (actividad crítica)
        while current_time < end_time:
            # Actividad muy intensa
            if random.random() < self.eruption_parameters['escalation_event_rate']:
                event = self._generate_critical_event(current_time)
                seismic_data.append(event)
            
            # Múltiples eventos por hora
            if random.random() < 0.7:  # 70% probabilidad de evento adicional
                current_time += timedelta(minutes=random.randint(10, 30))
                if current_time < end_time:
                    event = self._generate_critical_event(current_time)
                    seismic_data.append(event)
            
            current_time += timedelta(hours=1)
        
        return seismic_data
    
    def _generate_normal_event(self, timestamp: datetime) -> SeismicReading:
        """
        Genera un evento sísmico normal
        """
        return SeismicReading(
            timestamp=timestamp,
            magnitude=np.random.uniform(*self.base_parameters['normal_magnitude_range']),
            frequency=np.random.uniform(*self.base_parameters['normal_frequency_range']),
            duration=np.random.uniform(*self.base_parameters['normal_duration_range']),
            depth=np.random.uniform(*self.base_parameters['normal_depth_range'])
        )
    
    def _generate_escalated_event(
        self, 
        timestamp: datetime, 
        escalation_factor: float
    ) -> SeismicReading:
        """
        Genera un evento sísmico escalado
        """
        # Magnitud aumenta con escalación
        base_mag = np.random.uniform(*self.base_parameters['normal_magnitude_range'])
        magnitude = base_mag * (1 + escalation_factor * 0.3)
        
        # Frecuencia aumenta
        base_freq = np.random.uniform(*self.base_parameters['normal_frequency_range'])
        frequency = base_freq * (1 + escalation_factor * 0.4)
        
        # Duración aumenta
        base_dur = np.random.uniform(*self.base_parameters['normal_duration_range'])
        duration = base_dur * (1 + escalation_factor * 0.2)
        
        # Profundidad disminuye (más superficial)
        depth = np.random.uniform(*self.base_parameters['normal_depth_range'])
        depth = depth * (1 - escalation_factor * 0.1)
        
        return SeismicReading(
            timestamp=timestamp,
            magnitude=min(magnitude, 5.0),  # Límite máximo
            frequency=min(frequency, 20.0),
            duration=min(duration, 40.0),
            depth=max(depth, 0.5)  # Límite mínimo
        )
    
    def _generate_critical_event(self, timestamp: datetime) -> SeismicReading:
        """
        Genera un evento sísmico crítico pre-erupción
        """
        return SeismicReading(
            timestamp=timestamp,
            magnitude=np.random.uniform(*self.eruption_parameters['escalation_magnitude_range']),
            frequency=np.random.uniform(*self.eruption_parameters['escalation_frequency_range']),
            duration=np.random.uniform(*self.eruption_parameters['escalation_duration_range']),
            depth=np.random.uniform(*self.eruption_parameters['escalation_depth_range'])
        )
    
    def generate_real_time_data(
        self,
        scenario_type: str = "normal",
        timestamp: Optional[datetime] = None
    ) -> SeismicReading:
        """
        Genera un punto de datos en tiempo real
        
        Args:
            scenario_type: "normal", "escalated", o "critical"
            timestamp: Tiempo del evento
            
        Returns:
            Lectura sísmica individual
        """
        if timestamp is None:
            timestamp = datetime.utcnow()
        
        if scenario_type == "normal":
            return self._generate_normal_event(timestamp)
        elif scenario_type == "escalated":
            return self._generate_escalated_event(timestamp, 1.5)
        elif scenario_type == "critical":
            return self._generate_critical_event(timestamp)
        else:
            raise ValueError(f"Tipo de escenario no válido: {scenario_type}")
    
    def generate_pattern_data(
        self,
        pattern_type: str,
        hours: int = 24,
        start_time: Optional[datetime] = None
    ) -> List[SeismicReading]:
        """
        Genera patrones específicos de actividad sísmica
        
        Args:
            pattern_type: "linear_increase", "exponential_increase", "periodic", "random_bursts"
            hours: Duración en horas
            start_time: Tiempo de inicio
            
        Returns:
            Lista de lecturas sísmicas
        """
        if start_time is None:
            start_time = datetime.utcnow() - timedelta(hours=hours)
        
        seismic_data = []
        
        if pattern_type == "linear_increase":
            seismic_data = self._generate_linear_increase_pattern(start_time, hours)
        elif pattern_type == "exponential_increase":
            seismic_data = self._generate_exponential_increase_pattern(start_time, hours)
        elif pattern_type == "periodic":
            seismic_data = self._generate_periodic_pattern(start_time, hours)
        elif pattern_type == "random_bursts":
            seismic_data = self._generate_random_bursts_pattern(start_time, hours)
        else:
            raise ValueError(f"Tipo de patrón no válido: {pattern_type}")
        
        return seismic_data
    
    def _generate_linear_increase_pattern(
        self, 
        start_time: datetime, 
        hours: int
    ) -> List[SeismicReading]:
        """
        Genera patrón de aumento lineal
        """
        seismic_data = []
        
        for hour in range(hours):
            current_time = start_time + timedelta(hours=hour)
            
            # Aumento lineal de magnitud
            progress = hour / hours
            base_magnitude = 1.0 + progress * 3.0  # De 1.0 a 4.0
            
            # Generar 1-3 eventos por hora
            events_per_hour = 1 + int(progress * 2)
            
            for event in range(events_per_hour):
                event_time = current_time + timedelta(minutes=random.randint(0, 59))
                magnitude = base_magnitude + np.random.normal(0, 0.2)
                
                seismic_data.append(SeismicReading(
                    timestamp=event_time,
                    magnitude=max(magnitude, 0.1),
                    frequency=2.0 + progress * 5.0,
                    duration=5.0 + progress * 10.0,
                    depth=10.0 - progress * 5.0
                ))
        
        return seismic_data
    
    def _generate_exponential_increase_pattern(
        self, 
        start_time: datetime, 
        hours: int
    ) -> List[SeismicReading]:
        """
        Genera patrón de aumento exponencial
        """
        seismic_data = []
        
        for hour in range(hours):
            current_time = start_time + timedelta(hours=hour)
            
            # Aumento exponencial
            progress = hour / hours
            exponential_factor = np.exp(progress * 2) - 1  # e^(2x) - 1
            
            base_magnitude = 1.0 + exponential_factor * 0.5
            event_count = 1 + int(exponential_factor * 2)
            
            for event in range(event_count):
                event_time = current_time + timedelta(minutes=random.randint(0, 59))
                magnitude = base_magnitude + np.random.normal(0, 0.1)
                
                seismic_data.append(SeismicReading(
                    timestamp=event_time,
                    magnitude=max(magnitude, 0.1),
                    frequency=1.0 + exponential_factor * 2.0,
                    duration=3.0 + exponential_factor * 5.0,
                    depth=12.0 - exponential_factor * 3.0
                ))
        
        return seismic_data
    
    def _generate_periodic_pattern(
        self, 
        start_time: datetime, 
        hours: int
    ) -> List[SeismicReading]:
        """
        Genera patrón periódico
        """
        seismic_data = []
        period = 6  # Período de 6 horas
        
        for hour in range(hours):
            current_time = start_time + timedelta(hours=hour)
            
            # Función sinusoidal
            phase = (hour % period) / period * 2 * np.pi
            sine_value = (np.sin(phase) + 1) / 2  # Normalizar a 0-1
            
            magnitude = 1.5 + sine_value * 2.0
            frequency = 2.0 + sine_value * 4.0
            
            # Más eventos en los picos
            if sine_value > 0.7:
                event_count = 2
            else:
                event_count = 1
            
            for event in range(event_count):
                event_time = current_time + timedelta(minutes=random.randint(0, 59))
                
                seismic_data.append(SeismicReading(
                    timestamp=event_time,
                    magnitude=magnitude + np.random.normal(0, 0.1),
                    frequency=frequency + np.random.normal(0, 0.5),
                    duration=5.0 + sine_value * 5.0,
                    depth=8.0 + sine_value * 4.0
                ))
        
        return seismic_data
    
    def _generate_random_bursts_pattern(
        self, 
        start_time: datetime, 
        hours: int
    ) -> List[SeismicReading]:
        """
        Genera patrón de ráfagas aleatorias
        """
        seismic_data = []
        
        for hour in range(hours):
            current_time = start_time + timedelta(hours=hour)
            
            # 30% probabilidad de ráfaga
            if random.random() < 0.3:
                # Ráfaga de 3-8 eventos
                burst_count = random.randint(3, 8)
                burst_magnitude = 2.0 + random.random() * 2.0
                
                for event in range(burst_count):
                    event_time = current_time + timedelta(minutes=random.randint(0, 20))
                    
                    seismic_data.append(SeismicReading(
                        timestamp=event_time,
                        magnitude=burst_magnitude + np.random.normal(0, 0.3),
                        frequency=5.0 + random.random() * 5.0,
                        duration=2.0 + random.random() * 8.0,
                        depth=3.0 + random.random() * 6.0
                    ))
            else:
                # Actividad normal
                if random.random() < 0.5:
                    event_time = current_time + timedelta(minutes=random.randint(0, 59))
                    seismic_data.append(self._generate_normal_event(event_time))
        
        return seismic_data
    
    def get_scenario_description(self, scenario_type: str) -> Dict[str, Any]:
        """
        Obtiene descripción de un escenario
        """
        descriptions = {
            "normal": {
                "name": "Actividad Normal",
                "description": "Actividad sísmica volcánica de fondo normal",
                "characteristics": [
                    "Magnitud: 1.0-3.0",
                    "Frecuencia: 0.5-5.0 Hz",
                    "Tasa de eventos: 0.3/hora",
                    "Profundidad: 2-15 km"
                ]
            },
            "eruption": {
                "name": "Escenario Pre-Erupción",
                "description": "Secuencia aceleratoria hacia erupción",
                "characteristics": [
                    "Fase 1: Actividad normal (60%)",
                    "Fase 2: Escalada gradual (30%)",
                    "Fase 3: Actividad crítica (10%)",
                    "Aceleración de precursores",
                    "Clustering temporal"
                ]
            }
        }
        
        return descriptions.get(scenario_type, {"name": "Desconocido", "description": "Escenario no definido"})