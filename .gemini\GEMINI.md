# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Mobile App (Root)
```bash
# Development
npm run dev                 # Start Expo dev server
npm run start:all          # Start all services (mobile, backend, frontend)
npm run dev:all            # Same as start:all

# Platform specific
npm run android            # Run on Android
npm run ios               # Run on iOS  
npm run web               # Run on web

# Testing
npm test                  # Run Jest tests
npm run test:watch        # Run tests in watch mode
npm run test:coverage     # Run tests with coverage
npm run test:all          # Run all tests (mobile, backend, frontend)
npm run test:ci           # Run tests in CI mode

# Utilities
npm run diagnose          # Diagnose network issues
npm run check-network     # Check network connectivity
npm run reset-project     # Reset project to blank state
```

### Backend (backoffice/backend)
```bash
# Development
npm run dev               # Start with hot reload using nodemon
npm run build            # Compile TypeScript to dist/
npm run start            # Run compiled version

# Testing
npm test                 # Run Jest tests
npm run test:watch       # Run tests in watch mode
npm run test:coverage    # Run tests with coverage
npm run test:ci          # Run tests in CI mode
npm run test:controllers # Test only controllers
npm run test:services    # Test only services
npm run test:middleware  # Test only middleware

# Code Quality
npm run lint             # Run ESLint
npm run lint:fix         # Fix ESLint issues
npm run type-check       # TypeScript type checking only

# Database
npm run db:migrate       # Run database migrations
npm run db:seed         # Seed database with test data
```

### Frontend (backoffice/frontend)
```bash
# Development
npm run dev              # Start Vite dev server
npm run dev:debug        # Start with debug monitoring
npm run build           # Build for production
npm run preview         # Preview production build

# Testing
npm test                # Run Vitest tests
npm run test:ui         # Run tests with UI
npm run test:coverage   # Run tests with coverage
npm run test:watch      # Run tests in watch mode
npm run test:ci         # Run tests in CI mode
npm run test:components # Test only components
npm run test:hooks      # Test only hooks
npm run test:services   # Test only services

# Code Quality
npm run lint            # Run ESLint
npm run lint:fix        # Fix ESLint issues
npm run type-check      # TypeScript type checking only
```

## Architecture Overview

### Monorepo Structure
This is a monorepo containing three main applications:
- **Mobile App** (root): React Native/Expo app for volcano monitoring
- **Backend API** (backoffice/backend): Express/TypeScript API server
- **Frontend Dashboard** (backoffice/frontend): React/Vite admin dashboard

### Mobile App Architecture
- **Framework**: Expo with React Native
- **Navigation**: Expo Router with file-based routing
- **State Management**: React Query for server state, React Context for local state
- **Maps**: React Native Maps with Leaflet integration
- **Real-time**: WebSocket client for live updates
- **Offline**: AsyncStorage for local data persistence
- **Testing**: Jest with React Native Testing Library

Key directories:
- `app/` - File-based routing with tabs layout
- `components/` - Reusable UI components
- `services/` - API client, WebSocket, notifications
- `hooks/` - Custom React hooks
- `utils/` - Utility functions including precursor analysis

### Backend Architecture
- **Framework**: Express with TypeScript
- **Database**: Supabase (PostgreSQL)
- **Authentication**: JWT tokens with refresh
- **Real-time**: WebSocket with Socket.IO
- **Logging**: Winston with structured logging
- **Validation**: Express-validator and Joi
- **Testing**: Jest with Supertest

Key directories:
- `src/controllers/` - Request handling logic
- `src/middleware/` - Authentication, validation, error handling
- `src/routes/` - API route definitions
- `src/services/` - External service integrations (Supabase, WebSocket)
- `src/types/` - TypeScript type definitions

### Frontend Architecture
- **Framework**: React with TypeScript
- **Build Tool**: Vite
- **UI Library**: Shadcn/ui with Radix UI components
- **Styling**: Tailwind CSS
- **Maps**: React Leaflet for zone management
- **State Management**: React Query for server state
- **Testing**: Vitest with React Testing Library

Key directories:
- `src/components/` - UI components organized by feature
- `src/contexts/` - React Context providers
- `src/hooks/` - Custom React hooks
- `src/services/` - API client and utilities

## Key Features

### Mobile App Features
- Real-time volcano alert monitoring
- Interactive maps with safety zones
- Location tracking and safety status
- Push notifications for alerts
- Offline-first data synchronization
- Precursor Acceleration Index calculation

### Backend Features
- Mobile API endpoints for alerts and zones
- WebSocket real-time updates
- Location reporting and safety checks
- Admin authentication and authorization
- Audit logging and system monitoring
- Bulk data synchronization for mobile

### Frontend Features
- Administrative dashboard for alerts
- Zone management with map drawing
- Real-time monitoring and statistics
- User authentication and session management
- Alert creation and management interface

## Database Schema

### Key Tables
- `alerts` - Volcano alert information
- `zones` - Safety zone definitions with geospatial data
- `location_reports` - Anonymous location tracking
- `audit_logs` - System activity logging
- `users` - Administrative users
- `realtime_notifications` - WebSocket event tracking

## API Architecture

### Mobile API (`/api/mobile/`)
- `GET /alerts/current` - Get current active alert
- `GET /zones/all` - Get all safety zones
- `POST /location/report` - Report user location
- `POST /location/check` - Check location safety
- `POST /sync` - Bulk data synchronization

### Admin API (`/api/`)
- `POST /auth/login` - Admin authentication
- `GET /alerts` - List alerts with pagination
- `POST /alerts` - Create new alert
- `GET /zones` - List zones with pagination
- `POST /zones` - Create new zone

## Development Workflow

### Starting Development
1. Run `npm run start:all` to start all services
2. Mobile app available at Expo dev server
3. Backend API at http://localhost:3001
4. Frontend dashboard at http://localhost:5173

### Running Tests
Use `npm run test:all` to run all tests across all projects, or run tests individually in each project directory.

### Code Quality
All projects use ESLint and TypeScript. Run `npm run lint` and `npm run type-check` before committing.

### Debugging
- Backend: Uses Winston logging with different levels
- Frontend: Vite dev server with hot reload
- Mobile: Expo dev tools and React DevTools

## Configuration

### Environment Variables
Each project has its own `.env` file:
- **Backend**: Database, JWT secrets, CORS settings
- **Frontend**: API endpoints, authentication config
- **Mobile**: API base URLs, WebSocket endpoints

### Network Configuration
The system includes network diagnostics tools:
- `npm run diagnose` - Check connectivity issues
- `npm run check-network` - Verify API endpoints

## Testing Strategy

### Unit Tests
- Controllers, services, and utilities
- React components and hooks
- API endpoint testing with supertest

### Integration Tests
- Database operations
- WebSocket communications
- Cross-service interactions

### End-to-End Testing
- Mobile app workflows
- Admin dashboard operations
- Real-time alert propagation

## Security Considerations

- JWT authentication with refresh tokens
- Input validation on all endpoints
- Rate limiting on API endpoints
- CORS configuration for development/production
- Secure environment variable handling
- Anonymous location tracking (no PII)

## Performance Optimizations

- React Query for efficient data fetching
- Offline-first mobile architecture
- WebSocket for real-time updates
- Lazy loading in React components
- Database indexing for geospatial queries
- Caching strategies for static data

## Deployment Architecture

The application supports both development and production environments with different configurations for:
- API endpoints and WebSocket URLs
- Database connections
- Authentication secrets
- CORS policies
- Logging levels

## Important Notes

- The mobile app uses anonymous device IDs for location tracking
- All timestamps are in UTC
- Geospatial data uses WGS84 coordinate system
- WebSocket connections support automatic reconnection
- The system includes comprehensive error handling and logging
- Offline functionality ensures critical features work without connectivity