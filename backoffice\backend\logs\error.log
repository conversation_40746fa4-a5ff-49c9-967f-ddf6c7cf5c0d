{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:17:40.403Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:17:40.459Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:17:40.564Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:17:40.723Z"}
{"level":"error","message":"Failed to initialize Redis: Reached the max retries per request limit (which is 3). Refer to \"maxRetriesPerRequest\" option for details.","stack":"MaxRetriesPerRequestError: Reached the max retries per request limit (which is 3). Refer to \"maxRetriesPerRequest\" option for details.\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ioredis\\built\\redis\\event_handler.js:182:37)\n    at Object.onceWrapper (node:events:633:26)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:489:12)\n    at TCP.<anonymous> (node:net:351:12)","timestamp":"2025-06-01T05:17:40.726Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:17:40.940Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:17:41.200Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:17:41.522Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:17:41.882Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:17:42.299Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:17:42.756Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:17:43.267Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:17:43.829Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:17:44.436Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:17:45.092Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:17:45.809Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:17:46.566Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:17:47.381Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:17:48.248Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:17:49.152Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:17:50.104Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:17:51.109Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:17:52.169Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:17:53.273Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:17:54.427Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:17:55.629Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:17:56.887Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:17:58.191Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:17:59.550Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:18:00.960Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:18:02.418Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:18:03.927Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:18:05.479Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:18:07.093Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:18:08.755Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:18:10.460Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:18:12.221Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:18:14.031Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:18:15.894Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:18:17.800Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:18:19.758Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:18:21.763Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:18:23.772Z"}
{"error":{"message":"Route GET /api-docs not found not found","name":"Error","stack":"Error: Route GET /api-docs not found not found\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\middleware\\errorHandler.ts:168:19\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at param (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:421:3)\n    at next (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)"},"level":"error","message":"Unhandled Error","request":{"body":{},"headers":{"accept":"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8","accept-encoding":"gzip, deflate, br, zstd","accept-language":"es-CL,es;q=0.8,en-US;q=0.5,en;q=0.3","connection":"keep-alive","cookie":"ajs_anonymous_id=3183d747-d124-4f67-9ba3-3080b47f7358; _streamlit_xsrf=2|26cb962a|3d8bd72a30b1eb26c7f63610af6693f8|1747885207","host":"localhost:3001","priority":"u=0, i","sec-fetch-dest":"document","sec-fetch-mode":"navigate","sec-fetch-site":"none","sec-fetch-user":"?1","sec-gpc":"1","upgrade-insecure-requests":"1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0"},"method":"GET","requestId":"req_1748755105235_387xctk6p","url":"/api-docs"},"timestamp":"2025-06-01T05:18:25.239Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:18:25.784Z"}
{"error":{"message":"Route GET /favicon.ico not found not found","name":"Error","stack":"Error: Route GET /favicon.ico not found not found\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\middleware\\errorHandler.ts:168:19\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at param (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:421:3)\n    at next (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)"},"level":"error","message":"Unhandled Error","request":{"body":{},"headers":{"accept":"image/avif,image/jxl,image/webp,image/png,image/svg+xml,image/*;q=0.8,*/*;q=0.5","accept-encoding":"gzip, deflate, br, zstd","accept-language":"es-CL,es;q=0.8,en-US;q=0.5,en;q=0.3","connection":"keep-alive","host":"localhost:3001","priority":"u=6","sec-fetch-dest":"image","sec-fetch-mode":"no-cors","sec-fetch-site":"cross-site","sec-gpc":"1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0"},"method":"GET","requestId":"req_1748755107315_qvynao1w7","url":"/favicon.ico"},"timestamp":"2025-06-01T05:18:27.316Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:18:27.788Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:18:29.794Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:18:31.812Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:18:33.815Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:18:35.827Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:18:37.840Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:18:39.848Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:18:41.851Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:18:43.853Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:18:45.857Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:18:47.867Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:18:49.871Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:18:51.877Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:18:53.881Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:18:55.894Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:18:57.896Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:18:59.908Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:19:01.913Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:19:03.926Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:19:05.938Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:19:07.950Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:19:09.961Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:19:11.976Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:19:13.990Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:19:15.993Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:19:18.004Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:19:20.015Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:19:22.022Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:19:24.029Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:19:26.040Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:19:28.045Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:19:30.058Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:19:32.066Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:19:34.071Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:19:36.087Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:19:38.093Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:19:40.102Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:19:42.110Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:19:44.127Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:19:46.138Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:19:48.144Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:19:50.157Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:19:52.162Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:19:54.176Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:19:56.180Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:19:58.189Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:20:00.192Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:20:02.198Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:20:04.204Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:20:06.211Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:20:08.214Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:20:10.224Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:20:12.229Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:20:14.232Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:20:16.243Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:20:18.251Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:20:20.259Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:20:22.261Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:20:24.263Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:20:26.271Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:20:28.283Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:20:30.294Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:20:32.304Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:20:34.314Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:20:36.324Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:20:38.334Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:20:40.343Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:20:42.353Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:20:44.362Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:20:46.373Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:20:48.380Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:20:50.382Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:20:52.393Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:20:54.406Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:20:56.414Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:20:58.418Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:21:00.424Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:21:02.431Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:21:04.443Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:21:06.453Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:21:08.464Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:21:10.481Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:21:12.494Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:21:14.504Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:21:16.517Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:21:18.524Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:21:20.533Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:21:22.535Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:21:24.538Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","errno":-4078,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)","syscall":"connect","timestamp":"2025-06-01T05:21:26.542Z"}
{"address":"::1","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use ::1:3001","port":3001,"stack":"Error: listen EADDRINUSE: address already in use ::1:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at GetAddrInfoReqWrap.callback (node:net:2205:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:134:8)","syscall":"listen","timestamp":"2025-06-01T05:30:51.119Z"}
{"error":{"message":"Route GET /api-docs not found not found","name":"Error","stack":"Error: Route GET /api-docs not found not found\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\middleware\\errorHandler.ts:168:19\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at param (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:421:3)\n    at next (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)"},"level":"error","message":"Unhandled Error","request":{"body":{},"headers":{"accept":"*/*","host":"localhost:3002","user-agent":"curl/8.7.1"},"method":"GET","requestId":"req_1748755905907_rmt1ib1iv","url":"/api-docs"},"timestamp":"2025-06-01T05:31:45.910Z"}
{"error":{"message":"Route GET /api-docs not found not found","name":"Error","stack":"Error: Route GET /api-docs not found not found\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\middleware\\errorHandler.ts:168:19\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at param (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:421:3)\n    at next (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)"},"level":"error","message":"Unhandled Error","request":{"body":{},"headers":{"accept":"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8","accept-encoding":"gzip, deflate, br, zstd","accept-language":"es-CL,es;q=0.8,en-US;q=0.5,en;q=0.3","connection":"keep-alive","cookie":"ajs_anonymous_id=3183d747-d124-4f67-9ba3-3080b47f7358; _streamlit_xsrf=2|26cb962a|3d8bd72a30b1eb26c7f63610af6693f8|1747885207","host":"localhost:3002","priority":"u=0, i","sec-fetch-dest":"document","sec-fetch-mode":"navigate","sec-fetch-site":"none","sec-fetch-user":"?1","sec-gpc":"1","upgrade-insecure-requests":"1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0"},"method":"GET","requestId":"req_1748755950105_tvikzeonw","url":"/api-docs"},"timestamp":"2025-06-01T05:32:30.105Z"}
{"error":{"message":"Route GET /favicon.ico not found not found","name":"Error","stack":"Error: Route GET /favicon.ico not found not found\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\middleware\\errorHandler.ts:168:19\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at param (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:421:3)\n    at next (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)"},"level":"error","message":"Unhandled Error","request":{"body":{},"headers":{"accept":"image/avif,image/jxl,image/webp,image/png,image/svg+xml,image/*;q=0.8,*/*;q=0.5","accept-encoding":"gzip, deflate, br, zstd","accept-language":"es-CL,es;q=0.8,en-US;q=0.5,en;q=0.3","connection":"keep-alive","host":"localhost:3002","priority":"u=6","sec-fetch-dest":"image","sec-fetch-mode":"no-cors","sec-fetch-site":"cross-site","sec-gpc":"1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0"},"method":"GET","requestId":"req_1748755952183_j77h3ixhq","url":"/favicon.ico"},"timestamp":"2025-06-01T05:32:32.183Z"}
{"address":"::1","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use ::1:3001","port":3001,"stack":"Error: listen EADDRINUSE: address already in use ::1:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at GetAddrInfoReqWrap.callback (node:net:2205:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:134:8)","syscall":"listen","timestamp":"2025-06-01T05:51:59.345Z"}
{"address":"::1","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use ::1:3001","port":3001,"stack":"Error: listen EADDRINUSE: address already in use ::1:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at GetAddrInfoReqWrap.callback (node:net:2205:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:134:8)","syscall":"listen","timestamp":"2025-06-01T05:52:46.381Z"}
{"error":{"message":"Route GET /favicon.ico not found not found","name":"Error","stack":"Error: Route GET /favicon.ico not found not found\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\middleware\\errorHandler.ts:168:19\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at param (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:421:3)\n    at next (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)"},"level":"error","message":"Unhandled Error","request":{"body":{},"headers":{"accept":"image/avif,image/jxl,image/webp,image/png,image/svg+xml,image/*;q=0.8,*/*;q=0.5","accept-encoding":"gzip, deflate, br, zstd","accept-language":"es-CL,es;q=0.8,en-US;q=0.5,en;q=0.3","connection":"keep-alive","host":"localhost:3002","priority":"u=6","sec-fetch-dest":"image","sec-fetch-mode":"no-cors","sec-fetch-site":"cross-site","sec-gpc":"1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"},"method":"GET","requestId":"req_1748800255900_pkbcxvqfj","url":"/favicon.ico"},"timestamp":"2025-06-01T17:50:55.903Z"}
{"code":"22P02","details":null,"hint":null,"level":"error","message":"Error logging audit action: invalid input syntax for type uuid: \"dev-user-id\"","timestamp":"2025-06-01T20:13:16.023Z"}
{"code":"22P02","details":null,"hint":null,"level":"error","message":"Error logging audit action: invalid input syntax for type uuid: \"dev-user-id\"","timestamp":"2025-06-01T20:14:07.901Z"}
{"code":"22P02","details":null,"hint":null,"level":"error","message":"Error logging audit action: invalid input syntax for type uuid: \"dev-user-id\"","timestamp":"2025-06-01T20:14:24.145Z"}
{"address":"0.0.0.0","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use 0.0.0.0:3002","port":3002,"stack":"Error: listen EADDRINUSE: address already in use 0.0.0.0:3002\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at node:net:2205:7\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","syscall":"listen","timestamp":"2025-06-01T20:56:23.304Z"}
{"address":"0.0.0.0","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use 0.0.0.0:3002","port":3002,"stack":"Error: listen EADDRINUSE: address already in use 0.0.0.0:3002\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at node:net:2205:7\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","syscall":"listen","timestamp":"2025-06-01T20:56:39.219Z"}
{"address":"0.0.0.0","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use 0.0.0.0:3002","port":3002,"stack":"Error: listen EADDRINUSE: address already in use 0.0.0.0:3002\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at node:net:2205:7\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","syscall":"listen","timestamp":"2025-06-01T20:57:55.881Z"}
{"address":"0.0.0.0","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use 0.0.0.0:3002","port":3002,"stack":"Error: listen EADDRINUSE: address already in use 0.0.0.0:3002\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at node:net:2205:7\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","syscall":"listen","timestamp":"2025-06-01T20:58:30.430Z"}
{"address":"0.0.0.0","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use 0.0.0.0:3002","port":3002,"stack":"Error: listen EADDRINUSE: address already in use 0.0.0.0:3002\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at node:net:2205:7\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","syscall":"listen","timestamp":"2025-06-01T20:59:13.889Z"}
{"address":"0.0.0.0","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use 0.0.0.0:3002","port":3002,"stack":"Error: listen EADDRINUSE: address already in use 0.0.0.0:3002\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at node:net:2205:7\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","syscall":"listen","timestamp":"2025-06-01T21:05:41.350Z"}
{"level":"error","message":"Report location error:","timestamp":"2025-06-01T22:07:43.136Z"}
{"level":"error","message":"Report location error:","timestamp":"2025-06-01T22:07:44.354Z"}
{"level":"error","message":"Report location error:","timestamp":"2025-06-01T22:07:59.337Z"}
{"level":"error","message":"Report location error:","timestamp":"2025-06-01T22:08:00.547Z"}
{"address":"0.0.0.0","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use 0.0.0.0:3002","port":3002,"stack":"Error: listen EADDRINUSE: address already in use 0.0.0.0:3002\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at node:net:2205:7\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","syscall":"listen","timestamp":"2025-06-01T22:09:41.329Z"}
{"level":"error","message":"Report location error:","timestamp":"2025-06-01T22:11:48.792Z"}
{"code":"42P10","details":null,"hint":null,"level":"error","message":"Report location error: there is no unique or exclusion constraint matching the ON CONFLICT specification","timestamp":"2025-06-01T22:13:13.598Z"}
{"error":{"message":"Route GET /api-docs not found not found","name":"Error","stack":"Error: Route GET /api-docs not found not found\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\dist\\middleware\\errorHandler.js:143:23\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at param (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:421:3)\n    at next (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)"},"level":"error","message":"Unhandled Error","request":{"body":{},"headers":{"accept":"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8","accept-encoding":"gzip, deflate, br, zstd","accept-language":"es-CL,es;q=0.8,en-US;q=0.5,en;q=0.3","connection":"keep-alive","cookie":"ajs_anonymous_id=3183d747-d124-4f67-9ba3-3080b47f7358; _streamlit_xsrf=2|26cb962a|3d8bd72a30b1eb26c7f63610af6693f8|1747885207","host":"localhost:3001","priority":"u=0, i","sec-fetch-dest":"document","sec-fetch-mode":"navigate","sec-fetch-site":"none","sec-fetch-user":"?1","sec-gpc":"1","upgrade-insecure-requests":"1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"},"method":"GET","requestId":"req_1748829747141_hhkmmkkl9","url":"/api-docs"},"timestamp":"2025-06-02T02:02:27.143Z"}
{"error":{"message":"Route GET /favicon.ico not found not found","name":"Error","stack":"Error: Route GET /favicon.ico not found not found\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\dist\\middleware\\errorHandler.js:143:23\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at param (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:421:3)\n    at next (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)"},"level":"error","message":"Unhandled Error","request":{"body":{},"headers":{"accept":"image/avif,image/jxl,image/webp,image/png,image/svg+xml,image/*;q=0.8,*/*;q=0.5","accept-encoding":"gzip, deflate, br, zstd","accept-language":"es-CL,es;q=0.8,en-US;q=0.5,en;q=0.3","connection":"keep-alive","host":"localhost:3001","priority":"u=6","sec-fetch-dest":"image","sec-fetch-mode":"no-cors","sec-fetch-site":"cross-site","sec-gpc":"1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"},"method":"GET","requestId":"req_1748829747272_s2p6ltkne","url":"/favicon.ico"},"timestamp":"2025-06-02T02:02:27.273Z"}
{"error":{"message":"Route GET /api/api/alerts not found not found","name":"Error","stack":"Error: Route GET /api/api/alerts not found not found\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\dist\\middleware\\errorHandler.js:143:23\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at param (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:421:3)\n    at next (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)"},"level":"error","message":"Unhandled Error","request":{"body":{},"headers":{"accept":"application/json, text/plain, */*","accept-encoding":"gzip, deflate, br, zstd","accept-language":"es-CL,es;q=0.8,en-US;q=0.5,en;q=0.3","connection":"keep-alive","host":"localhost:3001","origin":"http://localhost:3000","referer":"http://localhost:3000/","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-site","sec-gpc":"1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"},"method":"GET","requestId":"req_1748829759543_j9evw832k","url":"/api/api/alerts"},"timestamp":"2025-06-02T02:02:39.544Z"}
{"error":{"message":"Route GET /api/api/zones not found not found","name":"Error","stack":"Error: Route GET /api/api/zones not found not found\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\dist\\middleware\\errorHandler.js:143:23\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at param (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:421:3)\n    at next (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)"},"level":"error","message":"Unhandled Error","request":{"body":{},"headers":{"accept":"application/json, text/plain, */*","accept-encoding":"gzip, deflate, br, zstd","accept-language":"es-CL,es;q=0.8,en-US;q=0.5,en;q=0.3","connection":"keep-alive","host":"localhost:3001","origin":"http://localhost:3000","referer":"http://localhost:3000/","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-site","sec-gpc":"1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"},"method":"GET","requestId":"req_1748829759544_6ojt42yej","url":"/api/api/zones"},"timestamp":"2025-06-02T02:02:39.547Z"}
{"error":{"message":"Route GET /api/api/alerts not found not found","name":"Error","stack":"Error: Route GET /api/api/alerts not found not found\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\dist\\middleware\\errorHandler.js:143:23\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at param (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:421:3)\n    at next (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)"},"level":"error","message":"Unhandled Error","request":{"body":{},"headers":{"accept":"application/json, text/plain, */*","accept-encoding":"gzip, deflate, br, zstd","accept-language":"es-CL,es;q=0.8,en-US;q=0.5,en;q=0.3","connection":"keep-alive","host":"localhost:3001","origin":"http://localhost:3000","referer":"http://localhost:3000/","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-site","sec-gpc":"1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"},"method":"GET","requestId":"req_1748829759561_k9yf6f7h8","url":"/api/api/alerts"},"timestamp":"2025-06-02T02:02:39.561Z"}
{"error":{"message":"Route GET /api/api/zones not found not found","name":"Error","stack":"Error: Route GET /api/api/zones not found not found\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\dist\\middleware\\errorHandler.js:143:23\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at param (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:421:3)\n    at next (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)"},"level":"error","message":"Unhandled Error","request":{"body":{},"headers":{"accept":"application/json, text/plain, */*","accept-encoding":"gzip, deflate, br, zstd","accept-language":"es-CL,es;q=0.8,en-US;q=0.5,en;q=0.3","connection":"keep-alive","host":"localhost:3001","origin":"http://localhost:3000","referer":"http://localhost:3000/","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-site","sec-gpc":"1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"},"method":"GET","requestId":"req_1748829759568_3ui2tfv2b","url":"/api/api/zones"},"timestamp":"2025-06-02T02:02:39.571Z"}
{"error":{"message":"Route GET /api/api/audit?limit=50 not found not found","name":"Error","stack":"Error: Route GET /api/api/audit?limit=50 not found not found\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\dist\\middleware\\errorHandler.js:143:23\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at param (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:421:3)\n    at next (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)"},"level":"error","message":"Unhandled Error","request":{"body":{},"headers":{"accept":"application/json, text/plain, */*","accept-encoding":"gzip, deflate, br, zstd","accept-language":"es-CL,es;q=0.8,en-US;q=0.5,en;q=0.3","connection":"keep-alive","host":"localhost:3001","origin":"http://localhost:3000","referer":"http://localhost:3000/","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-site","sec-gpc":"1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"},"method":"GET","requestId":"req_1748829759570_brmhzs9vu","url":"/api/api/audit?limit=50"},"timestamp":"2025-06-02T02:02:39.575Z"}
{"error":{"message":"Route GET /api/api/audit?limit=50 not found not found","name":"Error","stack":"Error: Route GET /api/api/audit?limit=50 not found not found\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\dist\\middleware\\errorHandler.js:143:23\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at param (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:421:3)\n    at next (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)"},"level":"error","message":"Unhandled Error","request":{"body":{},"headers":{"accept":"application/json, text/plain, */*","accept-encoding":"gzip, deflate, br, zstd","accept-language":"es-CL,es;q=0.8,en-US;q=0.5,en;q=0.3","connection":"keep-alive","host":"localhost:3001","origin":"http://localhost:3000","referer":"http://localhost:3000/","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-site","sec-gpc":"1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"},"method":"GET","requestId":"req_1748829759571_q9gh6wbxj","url":"/api/api/audit?limit=50"},"timestamp":"2025-06-02T02:02:39.578Z"}
{"error":{"message":"Route GET /api/api/alerts not found not found","name":"Error","stack":"Error: Route GET /api/api/alerts not found not found\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\middleware\\errorHandler.ts:168:19\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at param (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:421:3)\n    at next (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)"},"level":"error","message":"Unhandled Error","request":{"body":{},"headers":{"accept":"application/json, text/plain, */*","accept-encoding":"gzip, deflate, br, zstd","accept-language":"es-CL,es;q=0.8,en-US;q=0.5,en;q=0.3","connection":"keep-alive","host":"localhost:3001","origin":"http://localhost:3000","referer":"http://localhost:3000/","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-site","sec-gpc":"1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"},"method":"GET","requestId":"req_1748830051275_xat62pct9","url":"/api/api/alerts"},"timestamp":"2025-06-02T02:07:31.282Z"}
{"error":{"message":"Route GET /api/api/zones not found not found","name":"Error","stack":"Error: Route GET /api/api/zones not found not found\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\middleware\\errorHandler.ts:168:19\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at param (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:421:3)\n    at next (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)"},"level":"error","message":"Unhandled Error","request":{"body":{},"headers":{"accept":"application/json, text/plain, */*","accept-encoding":"gzip, deflate, br, zstd","accept-language":"es-CL,es;q=0.8,en-US;q=0.5,en;q=0.3","connection":"keep-alive","host":"localhost:3001","origin":"http://localhost:3000","referer":"http://localhost:3000/","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-site","sec-gpc":"1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"},"method":"GET","requestId":"req_1748830051276_j8socct1z","url":"/api/api/zones"},"timestamp":"2025-06-02T02:07:31.288Z"}
{"error":{"message":"Route GET /api/api/alerts not found not found","name":"Error","stack":"Error: Route GET /api/api/alerts not found not found\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\middleware\\errorHandler.ts:168:19\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at param (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:421:3)\n    at next (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)"},"level":"error","message":"Unhandled Error","request":{"body":{},"headers":{"accept":"application/json, text/plain, */*","accept-encoding":"gzip, deflate, br, zstd","accept-language":"es-CL,es;q=0.8,en-US;q=0.5,en;q=0.3","connection":"keep-alive","host":"localhost:3001","origin":"http://localhost:3000","referer":"http://localhost:3000/","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-site","sec-gpc":"1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"},"method":"GET","requestId":"req_1748830051294_jzl3tubrg","url":"/api/api/alerts"},"timestamp":"2025-06-02T02:07:31.295Z"}
{"error":{"message":"Route GET /api/api/zones not found not found","name":"Error","stack":"Error: Route GET /api/api/zones not found not found\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\middleware\\errorHandler.ts:168:19\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at param (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:421:3)\n    at next (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)"},"level":"error","message":"Unhandled Error","request":{"body":{},"headers":{"accept":"application/json, text/plain, */*","accept-encoding":"gzip, deflate, br, zstd","accept-language":"es-CL,es;q=0.8,en-US;q=0.5,en;q=0.3","connection":"keep-alive","host":"localhost:3001","origin":"http://localhost:3000","referer":"http://localhost:3000/","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-site","sec-gpc":"1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"},"method":"GET","requestId":"req_1748830051295_4govkdaca","url":"/api/api/zones"},"timestamp":"2025-06-02T02:07:31.300Z"}
{"error":{"message":"Route GET /api/api/audit?limit=50 not found not found","name":"Error","stack":"Error: Route GET /api/api/audit?limit=50 not found not found\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\middleware\\errorHandler.ts:168:19\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at param (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:421:3)\n    at next (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)"},"level":"error","message":"Unhandled Error","request":{"body":{},"headers":{"accept":"application/json, text/plain, */*","accept-encoding":"gzip, deflate, br, zstd","accept-language":"es-CL,es;q=0.8,en-US;q=0.5,en;q=0.3","connection":"keep-alive","host":"localhost:3001","origin":"http://localhost:3000","referer":"http://localhost:3000/","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-site","sec-gpc":"1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"},"method":"GET","requestId":"req_1748830051310_saba60cib","url":"/api/api/audit?limit=50"},"timestamp":"2025-06-02T02:07:31.312Z"}
{"error":{"message":"Route GET /api/api/audit?limit=50 not found not found","name":"Error","stack":"Error: Route GET /api/api/audit?limit=50 not found not found\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\middleware\\errorHandler.ts:168:19\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at param (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:421:3)\n    at next (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)"},"level":"error","message":"Unhandled Error","request":{"body":{},"headers":{"accept":"application/json, text/plain, */*","accept-encoding":"gzip, deflate, br, zstd","accept-language":"es-CL,es;q=0.8,en-US;q=0.5,en;q=0.3","connection":"keep-alive","host":"localhost:3001","origin":"http://localhost:3000","referer":"http://localhost:3000/","sec-fetch-dest":"empty","sec-fetch-mode":"cors","sec-fetch-site":"same-site","sec-gpc":"1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"},"method":"GET","requestId":"req_1748830051311_fqto35t1q","url":"/api/api/audit?limit=50"},"timestamp":"2025-06-02T02:07:31.314Z"}
{"error":{"message":"Route GET /api-docs not found not found","name":"Error","stack":"Error: Route GET /api-docs not found not found\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\middleware\\errorHandler.ts:168:19\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at param (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:421:3)\n    at next (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)"},"level":"error","message":"Unhandled Error","request":{"body":{},"headers":{"accept":"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8","accept-encoding":"gzip, deflate, br, zstd","accept-language":"es-CL,es;q=0.8,en-US;q=0.5,en;q=0.3","connection":"keep-alive","cookie":"ajs_anonymous_id=3183d747-d124-4f67-9ba3-3080b47f7358; _streamlit_xsrf=2|26cb962a|3d8bd72a30b1eb26c7f63610af6693f8|1747885207","host":"localhost:3001","priority":"u=0, i","sec-fetch-dest":"document","sec-fetch-mode":"navigate","sec-fetch-site":"none","sec-fetch-user":"?1","sec-gpc":"1","upgrade-insecure-requests":"1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"},"method":"GET","requestId":"req_1748830160880_7bhshh2o3","url":"/api-docs"},"timestamp":"2025-06-02T02:09:20.881Z"}
{"error":{"message":"Route GET /favicon.ico not found not found","name":"Error","stack":"Error: Route GET /favicon.ico not found not found\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\middleware\\errorHandler.ts:168:19\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at param (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:421:3)\n    at next (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)"},"level":"error","message":"Unhandled Error","request":{"body":{},"headers":{"accept":"image/avif,image/jxl,image/webp,image/png,image/svg+xml,image/*;q=0.8,*/*;q=0.5","accept-encoding":"gzip, deflate, br, zstd","accept-language":"es-CL,es;q=0.8,en-US;q=0.5,en;q=0.3","connection":"keep-alive","host":"localhost:3001","priority":"u=6","sec-fetch-dest":"image","sec-fetch-mode":"no-cors","sec-fetch-site":"cross-site","sec-gpc":"1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"},"method":"GET","requestId":"req_1748830161004_kj8o2eqep","url":"/favicon.ico"},"timestamp":"2025-06-02T02:09:21.005Z"}
{"error":{"code":"22P02","details":null,"hint":null,"message":"invalid input syntax for type uuid: \"dev-user-id\""},"level":"error","message":"Database Operation Error","operation":"INSERT","table":"volcano_alerts","timestamp":"2025-06-02T02:13:16.570Z","userId":"dev-user-id"}
{"code":"22P02","details":null,"hint":null,"level":"error","message":"Create alert error: invalid input syntax for type uuid: \"dev-user-id\"","timestamp":"2025-06-02T02:13:16.570Z"}
{"code":"22P02","details":null,"hint":null,"level":"error","message":"Error logging audit action: invalid input syntax for type uuid: \"dev-user-id\"","timestamp":"2025-06-02T02:13:26.336Z"}
{"address":"0.0.0.0","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use 0.0.0.0:3001","port":3001,"stack":"Error: listen EADDRINUSE: address already in use 0.0.0.0:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at node:net:2205:7\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","syscall":"listen","timestamp":"2025-06-02T13:24:00.855Z"}
{"address":"0.0.0.0","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use 0.0.0.0:3001","port":3001,"stack":"Error: listen EADDRINUSE: address already in use 0.0.0.0:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at node:net:2205:7\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","syscall":"listen","timestamp":"2025-06-02T14:37:39.460Z"}
{"error":{"code":"22P02","details":null,"hint":null,"message":"invalid input syntax for type uuid: \"dev-user-id\""},"level":"error","message":"Database Operation Error","operation":"INSERT","table":"safety_zones","timestamp":"2025-06-02T15:47:58.772Z","userId":"dev-user-id"}
{"code":"22P02","details":null,"hint":null,"level":"error","message":"Create zone error: invalid input syntax for type uuid: \"dev-user-id\"","timestamp":"2025-06-02T15:47:58.773Z"}
{"error":{"code":"23503","details":"Key (created_by)=(00000000-0000-0000-0000-000000000001) is not present in table \"admin_users\".","hint":null,"message":"insert or update on table \"safety_zones\" violates foreign key constraint \"safety_zones_created_by_fkey\""},"level":"error","message":"Database Operation Error","operation":"INSERT","table":"safety_zones","timestamp":"2025-06-02T15:48:59.855Z","userId":"00000000-0000-0000-0000-000000000001"}
{"code":"23503","details":"Key (created_by)=(00000000-0000-0000-0000-000000000001) is not present in table \"admin_users\".","hint":null,"level":"error","message":"Create zone error: insert or update on table \"safety_zones\" violates foreign key constraint \"safety_zones_created_by_fkey\"","timestamp":"2025-06-02T15:48:59.856Z"}
{"error":{"code":"23503","details":"Key (created_by)=(00000000-0000-0000-0000-000000000001) is not present in table \"admin_users\".","hint":null,"message":"insert or update on table \"safety_zones\" violates foreign key constraint \"safety_zones_created_by_fkey\""},"level":"error","message":"Database Operation Error","operation":"INSERT","table":"safety_zones","timestamp":"2025-06-02T15:49:18.320Z","userId":"00000000-0000-0000-0000-000000000001"}
{"code":"23503","details":"Key (created_by)=(00000000-0000-0000-0000-000000000001) is not present in table \"admin_users\".","hint":null,"level":"error","message":"Create zone error: insert or update on table \"safety_zones\" violates foreign key constraint \"safety_zones_created_by_fkey\"","timestamp":"2025-06-02T15:49:18.321Z"}
{"code":"23503","details":"Key (user_id)=(00000000-0000-0000-0000-000000000001) is not present in table \"admin_users\".","hint":null,"level":"error","message":"Error logging audit action: insert or update on table \"audit_logs\" violates foreign key constraint \"audit_logs_user_id_fkey\"","timestamp":"2025-06-02T15:50:23.678Z"}
{"code":"23503","details":"Key (user_id)=(00000000-0000-0000-0000-000000000001) is not present in table \"admin_users\".","hint":null,"level":"error","message":"Error logging audit action: insert or update on table \"audit_logs\" violates foreign key constraint \"audit_logs_user_id_fkey\"","timestamp":"2025-06-02T16:04:17.727Z"}
{"code":"23503","details":"Key (user_id)=(00000000-0000-0000-0000-000000000001) is not present in table \"admin_users\".","hint":null,"level":"error","message":"Error logging audit action: insert or update on table \"audit_logs\" violates foreign key constraint \"audit_logs_user_id_fkey\"","timestamp":"2025-06-02T16:27:34.018Z"}
{"error":{"code":"23503","details":"Key (created_by)=(00000000-0000-0000-0000-000000000001) is not present in table \"admin_users\".","hint":null,"message":"insert or update on table \"volcano_alerts\" violates foreign key constraint \"volcano_alerts_created_by_fkey\""},"level":"error","message":"Database Operation Error","operation":"INSERT","table":"volcano_alerts","timestamp":"2025-06-02T18:07:16.325Z","userId":"00000000-0000-0000-0000-000000000001"}
{"code":"23503","details":"Key (created_by)=(00000000-0000-0000-0000-000000000001) is not present in table \"admin_users\".","hint":null,"level":"error","message":"Create alert error: insert or update on table \"volcano_alerts\" violates foreign key constraint \"volcano_alerts_created_by_fkey\"","timestamp":"2025-06-02T18:07:16.326Z"}
{"code":"23503","details":"Key (user_id)=(00000000-0000-0000-0000-000000000001) is not present in table \"admin_users\".","hint":null,"level":"error","message":"Error logging audit action: insert or update on table \"audit_logs\" violates foreign key constraint \"audit_logs_user_id_fkey\"","timestamp":"2025-06-02T18:08:04.484Z"}
{"code":"23503","details":"Key (user_id)=(00000000-0000-0000-0000-000000000001) is not present in table \"admin_users\".","hint":null,"level":"error","message":"Error logging audit action: insert or update on table \"audit_logs\" violates foreign key constraint \"audit_logs_user_id_fkey\"","timestamp":"2025-06-02T18:08:39.890Z"}
{"code":"23503","details":"Key (user_id)=(00000000-0000-0000-0000-000000000001) is not present in table \"admin_users\".","hint":null,"level":"error","message":"Error logging audit action: insert or update on table \"audit_logs\" violates foreign key constraint \"audit_logs_user_id_fkey\"","timestamp":"2025-06-02T18:08:57.893Z"}
{"error":{"message":"Route GET /favicon.ico not found not found","name":"Error","stack":"Error: Route GET /favicon.ico not found not found\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\middleware\\errorHandler.ts:168:19\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at param (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:421:3)\n    at next (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)"},"level":"error","message":"Unhandled Error","request":{"body":{},"headers":{"accept":"image/avif,image/jxl,image/webp,image/png,image/svg+xml,image/*;q=0.8,*/*;q=0.5","accept-encoding":"gzip, deflate, br, zstd","accept-language":"es-CL,es;q=0.8,en-US;q=0.5,en;q=0.3","connection":"keep-alive","host":"localhost:3001","priority":"u=6","sec-fetch-dest":"image","sec-fetch-mode":"no-cors","sec-fetch-site":"cross-site","sec-gpc":"1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"},"method":"GET","requestId":"req_1748888667751_w9564rynu","url":"/favicon.ico"},"timestamp":"2025-06-02T18:24:27.761Z"}
{"code":"","details":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at runNextTicks (node:internal/process/task_queues:69:3)\n    at processImmediate (node:internal/timers:453:9)\n    at async checkDatabaseConnection (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\services\\supabase.ts:74:23)\n    at async healthCheck (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\services\\supabase.ts:327:23)\n    at async C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\index.ts:109:20","hint":"","level":"error","message":"Database connection check failed: TypeError: fetch failed","timestamp":"2025-06-02T18:28:31.268Z"}
{"error":{"code":"","details":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at runNextTicks (node:internal/process/task_queues:69:3)\n    at processImmediate (node:internal/timers:453:9)\n    at async getAlerts (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\controllers\\alerts.ts:86:44)","hint":"","message":"TypeError: fetch failed"},"level":"error","message":"Database Operation Error","operation":"SELECT","table":"volcano_alerts","timestamp":"2025-06-02T18:28:31.281Z","userId":"00000000-0000-0000-0000-000000000001"}
{"code":"","details":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at runNextTicks (node:internal/process/task_queues:69:3)\n    at processImmediate (node:internal/timers:453:9)\n    at async getAlerts (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\controllers\\alerts.ts:86:44)","hint":"","level":"error","message":"Get alerts error: TypeError: fetch failed","timestamp":"2025-06-02T18:28:31.283Z"}
{"error":{"code":"","details":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at runNextTicks (node:internal/process/task_queues:69:3)\n    at processImmediate (node:internal/timers:453:9)\n    at async getAlerts (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\controllers\\alerts.ts:86:44)","hint":"","message":"TypeError: fetch failed"},"level":"error","message":"Database Operation Error","operation":"SELECT","table":"volcano_alerts","timestamp":"2025-06-02T18:28:31.286Z","userId":"00000000-0000-0000-0000-000000000001"}
{"code":"","details":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at runNextTicks (node:internal/process/task_queues:69:3)\n    at processImmediate (node:internal/timers:453:9)\n    at async getAlerts (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\controllers\\alerts.ts:86:44)","hint":"","level":"error","message":"Get alerts error: TypeError: fetch failed","timestamp":"2025-06-02T18:28:31.287Z"}
{"code":"","details":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at runNextTicks (node:internal/process/task_queues:69:3)\n    at processImmediate (node:internal/timers:453:9)\n    at async checkDatabaseConnection (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\services\\supabase.ts:74:23)\n    at async healthCheck (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\services\\supabase.ts:327:23)\n    at async C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\index.ts:109:20","hint":"","level":"error","message":"Database connection check failed: TypeError: fetch failed","timestamp":"2025-06-02T18:28:31.290Z"}
{"code":"","details":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at runNextTicks (node:internal/process/task_queues:69:3)\n    at processImmediate (node:internal/timers:453:9)\n    at async getAuditLogs (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\controllers\\audit.ts:89:47)","hint":"","level":"error","message":"Get audit logs error: TypeError: fetch failed","timestamp":"2025-06-02T18:28:31.301Z"}
{"error":{"code":"","details":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async getZones (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\controllers\\zones.ts:79:43)","hint":"","message":"TypeError: fetch failed"},"level":"error","message":"Database Operation Error","operation":"SELECT","table":"safety_zones","timestamp":"2025-06-02T18:28:31.307Z","userId":"00000000-0000-0000-0000-000000000001"}
{"code":"","details":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async getZones (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\controllers\\zones.ts:79:43)","hint":"","level":"error","message":"Get zones error: TypeError: fetch failed","timestamp":"2025-06-02T18:28:31.308Z"}
{"error":{"code":"","details":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async getZones (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\controllers\\zones.ts:79:43)","hint":"","message":"TypeError: fetch failed"},"level":"error","message":"Database Operation Error","operation":"SELECT","table":"safety_zones","timestamp":"2025-06-02T18:28:40.683Z","userId":"00000000-0000-0000-0000-000000000001"}
{"code":"","details":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async getZones (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\controllers\\zones.ts:79:43)","hint":"","level":"error","message":"Get zones error: TypeError: fetch failed","timestamp":"2025-06-02T18:28:40.684Z"}
{"address":"0.0.0.0","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use 0.0.0.0:3001","port":3001,"stack":"Error: listen EADDRINUSE: address already in use 0.0.0.0:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at node:net:2205:7\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","syscall":"listen","timestamp":"2025-06-02T18:36:01.308Z"}
{"code":"23503","details":"Key (user_id)=(00000000-0000-0000-0000-000000000001) is not present in table \"admin_users\".","hint":null,"level":"error","message":"Error logging audit action: insert or update on table \"audit_logs\" violates foreign key constraint \"audit_logs_user_id_fkey\"","timestamp":"2025-06-02T19:26:47.746Z"}
{"code":"","details":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async checkDatabaseConnection (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\services\\supabase.ts:74:23)\n    at async initializeSupabase (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\services\\supabase.ts:363:25)\n    at async startServer (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\index.ts:227:5)","hint":"","level":"error","message":"Database connection check failed: TypeError: fetch failed","timestamp":"2025-06-16T16:36:32.608Z"}
{"code":"","details":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async checkDatabaseConnection (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\services\\supabase.ts:74:23)\n    at async initializeSupabase (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\services\\supabase.ts:363:25)\n    at async startServer (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\index.ts:227:5)","hint":"","level":"error","message":"Database connection check failed: TypeError: fetch failed","timestamp":"2025-06-16T16:44:08.117Z"}
{"code":"","details":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async checkDatabaseConnection (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\services\\supabase.ts:74:23)\n    at async initializeSupabase (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\services\\supabase.ts:363:25)\n    at async startServer (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\index.ts:227:5)","hint":"","level":"error","message":"Database connection check failed: TypeError: fetch failed","timestamp":"2025-06-16T16:52:35.793Z"}
{"code":"","details":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async checkDatabaseConnection (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\services\\supabase.ts:74:23)\n    at async initializeSupabase (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\services\\supabase.ts:363:25)\n    at async startServer (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\index.ts:227:5)","hint":"","level":"error","message":"Database connection check failed: TypeError: fetch failed","timestamp":"2025-06-16T17:38:06.969Z"}
{"error":{"message":"Route GET /favicon.ico not found not found","name":"Error","stack":"Error: Route GET /favicon.ico not found not found\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\middleware\\errorHandler.ts:168:19\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at param (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:421:3)\n    at next (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)"},"level":"error","message":"Unhandled Error","request":{"body":{},"headers":{"accept":"image/avif,image/jxl,image/webp,image/png,image/svg+xml,image/*;q=0.8,*/*;q=0.5","accept-encoding":"gzip, deflate, br, zstd","accept-language":"es-CL,es;q=0.8,en-US;q=0.5,en;q=0.3","connection":"keep-alive","host":"localhost:3001","priority":"u=6","sec-fetch-dest":"image","sec-fetch-mode":"no-cors","sec-fetch-site":"cross-site","sec-gpc":"1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"},"method":"GET","requestId":"req_1750095749486_pmq2gkrwv","url":"/favicon.ico"},"timestamp":"2025-06-16T17:42:29.491Z"}
{"code":"23503","details":"Key (user_id)=(00000000-0000-0000-0000-000000000001) is not present in table \"admin_users\".","hint":null,"level":"error","message":"Error logging audit action: insert or update on table \"audit_logs\" violates foreign key constraint \"audit_logs_user_id_fkey\"","timestamp":"2025-06-16T18:52:06.737Z"}
{"code":"23503","details":"Key (user_id)=(00000000-0000-0000-0000-000000000001) is not present in table \"admin_users\".","hint":null,"level":"error","message":"Error logging audit action: insert or update on table \"audit_logs\" violates foreign key constraint \"audit_logs_user_id_fkey\"","timestamp":"2025-06-16T18:52:26.686Z"}
{"code":"23503","details":"Key (user_id)=(00000000-0000-0000-0000-000000000001) is not present in table \"admin_users\".","hint":null,"level":"error","message":"Error logging audit action: insert or update on table \"audit_logs\" violates foreign key constraint \"audit_logs_user_id_fkey\"","timestamp":"2025-06-16T18:52:49.299Z"}
{"error":{"code":"23503","details":"Key (created_by)=(00000000-0000-0000-0000-000000000001) is not present in table \"admin_users\".","hint":null,"message":"insert or update on table \"volcano_alerts\" violates foreign key constraint \"volcano_alerts_created_by_fkey\""},"level":"error","message":"Database Operation Error","operation":"INSERT","table":"volcano_alerts","timestamp":"2025-06-16T18:53:42.946Z","userId":"00000000-0000-0000-0000-000000000001"}
{"code":"23503","details":"Key (created_by)=(00000000-0000-0000-0000-000000000001) is not present in table \"admin_users\".","hint":null,"level":"error","message":"Create alert error: insert or update on table \"volcano_alerts\" violates foreign key constraint \"volcano_alerts_created_by_fkey\"","timestamp":"2025-06-16T18:53:42.947Z"}
{"error":{"code":"23503","details":"Key (created_by)=(00000000-0000-0000-0000-000000000001) is not present in table \"admin_users\".","hint":null,"message":"insert or update on table \"volcano_alerts\" violates foreign key constraint \"volcano_alerts_created_by_fkey\""},"level":"error","message":"Database Operation Error","operation":"INSERT","table":"volcano_alerts","timestamp":"2025-06-16T18:55:32.500Z","userId":"00000000-0000-0000-0000-000000000001"}
{"code":"23503","details":"Key (created_by)=(00000000-0000-0000-0000-000000000001) is not present in table \"admin_users\".","hint":null,"level":"error","message":"Create alert error: insert or update on table \"volcano_alerts\" violates foreign key constraint \"volcano_alerts_created_by_fkey\"","timestamp":"2025-06-16T18:55:32.501Z"}
{"error":{"code":"23503","details":"Key (created_by)=(00000000-0000-0000-0000-000000000001) is not present in table \"admin_users\".","hint":null,"message":"insert or update on table \"volcano_alerts\" violates foreign key constraint \"volcano_alerts_created_by_fkey\""},"level":"error","message":"Database Operation Error","operation":"INSERT","table":"volcano_alerts","timestamp":"2025-06-16T18:55:33.535Z","userId":"00000000-0000-0000-0000-000000000001"}
{"code":"23503","details":"Key (created_by)=(00000000-0000-0000-0000-000000000001) is not present in table \"admin_users\".","hint":null,"level":"error","message":"Create alert error: insert or update on table \"volcano_alerts\" violates foreign key constraint \"volcano_alerts_created_by_fkey\"","timestamp":"2025-06-16T18:55:33.536Z"}
{"error":{"code":"42703","details":null,"hint":null,"message":"column safety_zones.center_lat does not exist"},"level":"error","message":"Database Operation Error","operation":"SELECT","table":"safety_zones","timestamp":"2025-06-16T19:07:20.918Z","userId":"00000000-0000-0000-0000-000000000001"}
{"code":"42703","details":null,"hint":null,"level":"error","message":"Get zones error: column safety_zones.center_lat does not exist","timestamp":"2025-06-16T19:07:20.919Z"}
{"error":{"code":"42703","details":null,"hint":null,"message":"column safety_zones.center_lat does not exist"},"level":"error","message":"Database Operation Error","operation":"SELECT","table":"safety_zones","timestamp":"2025-06-16T19:07:21.132Z","userId":"00000000-0000-0000-0000-000000000001"}
{"code":"42703","details":null,"hint":null,"level":"error","message":"Get zones error: column safety_zones.center_lat does not exist","timestamp":"2025-06-16T19:07:21.132Z"}
{"error":{"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'center_lat' column of 'safety_zones' in the schema cache"},"level":"error","message":"Database Operation Error","operation":"INSERT","table":"safety_zones","timestamp":"2025-06-16T19:10:58.793Z","userId":"00000000-0000-0000-0000-000000000001"}
{"code":"PGRST204","details":null,"hint":null,"level":"error","message":"Create zone error: Could not find the 'center_lat' column of 'safety_zones' in the schema cache","timestamp":"2025-06-16T19:10:58.793Z"}
{"error":{"code":"42703","details":null,"hint":null,"message":"column safety_zones.center_lat does not exist"},"level":"error","message":"Database Operation Error","operation":"SELECT","table":"safety_zones","timestamp":"2025-06-16T19:17:04.695Z","userId":"00000000-0000-0000-0000-000000000001"}
{"code":"42703","details":null,"hint":null,"level":"error","message":"Get zones error: column safety_zones.center_lat does not exist","timestamp":"2025-06-16T19:17:04.696Z"}
{"error":{"code":"23503","details":"Key (created_by)=(00000000-0000-0000-0000-000000000001) is not present in table \"admin_users\".","hint":null,"message":"insert or update on table \"volcano_alerts\" violates foreign key constraint \"volcano_alerts_created_by_fkey\""},"level":"error","message":"Database Operation Error","operation":"INSERT","table":"volcano_alerts","timestamp":"2025-06-16T19:22:34.341Z","userId":"00000000-0000-0000-0000-000000000001"}
{"code":"23503","details":"Key (created_by)=(00000000-0000-0000-0000-000000000001) is not present in table \"admin_users\".","hint":null,"level":"error","message":"Create alert error: insert or update on table \"volcano_alerts\" violates foreign key constraint \"volcano_alerts_created_by_fkey\"","timestamp":"2025-06-16T19:22:34.342Z"}
{"error":{"code":"22007","details":null,"hint":null,"message":"invalid input syntax for type timestamp with time zone: \"\""},"level":"error","message":"Database Operation Error","operation":"UPDATE","recordId":"d40fb10c-5a56-4798-acff-42367e632f98","table":"volcano_alerts","timestamp":"2025-06-16T19:32:23.605Z","userId":"00000000-0000-0000-0000-000000000001"}
{"code":"22007","details":null,"hint":null,"level":"error","message":"Update alert error: invalid input syntax for type timestamp with time zone: \"\"","timestamp":"2025-06-16T19:32:23.606Z"}
{"error":{"message":"Route GET /favicon.ico not found not found","name":"Error","stack":"Error: Route GET /favicon.ico not found not found\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\middleware\\errorHandler.ts:168:19\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at param (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:421:3)\n    at next (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)"},"level":"error","message":"Unhandled Error","request":{"body":{},"headers":{"accept":"image/avif,image/jxl,image/webp,image/png,image/svg+xml,image/*;q=0.8,*/*;q=0.5","accept-encoding":"gzip, deflate, br, zstd","accept-language":"es-CL,es;q=0.8,en-US;q=0.5,en;q=0.3","connection":"keep-alive","host":"localhost:3001","priority":"u=6","sec-fetch-dest":"image","sec-fetch-mode":"no-cors","sec-fetch-site":"cross-site","sec-gpc":"1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"},"method":"GET","requestId":"req_1750735713090_ghjklobet","url":"/favicon.ico"},"timestamp":"2025-06-24T03:28:33.096Z"}
{"level":"error","message":"Database connection check failed: upstream connect error or disconnect/reset before headers. retried and the latest reset reason: remote connection failure, transport failure reason: delayed connect error: 111","timestamp":"2025-07-03T21:16:09.630Z"}
