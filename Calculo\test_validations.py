#!/usr/bin/env python3
"""
Script de prueba para validar las mejoras en el sistema de derivadas
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import numpy as np
from datetime import datetime, timedelta
from core.data_generator import SeismicDataGenerator
from core.derivatives import DerivativeCalculator
from models.volcanic_data import SeismicReading
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_normal_classification():
    """Prueba que actividad normal se clasifique correctamente"""
    print("🧪 PRUEBA 1: Clasificación de Actividad Normal")
    print("-" * 50)
    
    generator = SeismicDataGenerator(seed=42)
    calculator = DerivativeCalculator()
    
    # Generar datos normales
    normal_data = generator.generate_seismic_sequence(days=3, eruption_scenario=False)
    result = calculator.calculate_derivatives(normal_data, show_steps=False)
    
    print(f"Datos generados: {len(normal_data)} eventos")
    print(f"Max aceleración: {result.max_acceleration:.6f}")
    print(f"Tendencia: {result.acceleration_trend}")
    
    # Verificar clasificación
    if result.acceleration_trend == "ACTIVIDAD ESTABLE":
        print("✅ ÉXITO: Actividad normal clasificada correctamente")
        return True
    else:
        print(f"❌ FALLO: Actividad normal clasificada como {result.acceleration_trend}")
        return False

def test_eruption_classification():
    """Prueba que actividad pre-erupción se detecte correctamente"""
    print("\n🧪 PRUEBA 2: Detección de Actividad Pre-Erupción")
    print("-" * 50)
    
    generator = SeismicDataGenerator(seed=42)
    calculator = DerivativeCalculator()
    
    # Generar datos pre-erupción
    eruption_data = generator.generate_seismic_sequence(days=3, eruption_scenario=True)
    result = calculator.calculate_derivatives(eruption_data, show_steps=False)
    
    print(f"Datos generados: {len(eruption_data)} eventos")
    print(f"Max aceleración: {result.max_acceleration:.6f}")
    print(f"Tendencia: {result.acceleration_trend}")
    
    # Verificar detección
    if "CRÍTICA" in result.acceleration_trend or "MODERADA" in result.acceleration_trend:
        print("✅ ÉXITO: Actividad pre-erupción detectada correctamente")
        return True
    else:
        print(f"❌ FALLO: Actividad pre-erupción no detectada: {result.acceleration_trend}")
        return False

def test_outlier_handling():
    """Prueba el manejo de valores anómalos"""
    print("\n🧪 PRUEBA 3: Manejo de Valores Anómalos")
    print("-" * 50)
    
    calculator = DerivativeCalculator()
    base_time = datetime.now()
    
    # Crear datos con outliers
    magnitudes = [2.0, 2.1, 2.2, 15.0, 2.4, 2.5, 2.6, -5.0, 2.8, 2.9]  # Outliers: 15.0 y -5.0
    seismic_data = [
        SeismicReading(
            timestamp=base_time + timedelta(hours=i),
            magnitude=mag,
            frequency=3.0,
            duration=5.0,
            depth=10.0
        )
        for i, mag in enumerate(magnitudes)
    ]
    
    print(f"Datos originales: {magnitudes}")
    
    result = calculator.calculate_derivatives(seismic_data, show_steps=False)
    
    print(f"Max aceleración: {result.max_acceleration:.6f}")
    print(f"Tendencia: {result.acceleration_trend}")
    
    # Verificar que no hay valores extremos
    if result.max_acceleration < 50:  # Límite razonable
        print("✅ ÉXITO: Valores anómalos manejados correctamente")
        return True
    else:
        print(f"❌ FALLO: Valores extremos no controlados: {result.max_acceleration}")
        return False

def test_edge_cases():
    """Prueba casos extremos"""
    print("\n🧪 PRUEBA 4: Casos Extremos")
    print("-" * 50)
    
    calculator = DerivativeCalculator()
    base_time = datetime.now()
    
    # Caso 1: Datos constantes
    print("Subcaso 4.1: Datos constantes")
    constant_data = [
        SeismicReading(
            timestamp=base_time + timedelta(hours=i),
            magnitude=2.5,
            frequency=3.0,
            duration=5.0,
            depth=10.0
        )
        for i in range(10)
    ]
    
    result = calculator.calculate_derivatives(constant_data, show_steps=False)
    print(f"  Max aceleración: {result.max_acceleration:.6f}")
    print(f"  Tendencia: {result.acceleration_trend}")
    
    if result.acceleration_trend == "ACTIVIDAD ESTABLE":
        print("  ✅ Datos constantes clasificados correctamente")
        success1 = True
    else:
        print(f"  ❌ Datos constantes mal clasificados: {result.acceleration_trend}")
        success1 = False
    
    # Caso 2: Incremento lineal muy pequeño
    print("\nSubcaso 4.2: Incremento lineal pequeño")
    linear_data = [
        SeismicReading(
            timestamp=base_time + timedelta(hours=i),
            magnitude=2.0 + i * 0.01,  # Incremento muy pequeño
            frequency=3.0,
            duration=5.0,
            depth=10.0
        )
        for i in range(10)
    ]
    
    result = calculator.calculate_derivatives(linear_data, show_steps=False)
    print(f"  Max aceleración: {result.max_acceleration:.6f}")
    print(f"  Tendencia: {result.acceleration_trend}")
    
    if result.acceleration_trend == "ACTIVIDAD ESTABLE":
        print("  ✅ Incremento lineal pequeño clasificado correctamente")
        success2 = True
    else:
        print(f"  ❌ Incremento lineal pequeño mal clasificado: {result.acceleration_trend}")
        success2 = False
    
    return success1 and success2

def test_performance():
    """Prueba el rendimiento con datasets grandes"""
    print("\n🧪 PRUEBA 5: Rendimiento con Datasets Grandes")
    print("-" * 50)
    
    import time
    
    generator = SeismicDataGenerator(seed=42)
    calculator = DerivativeCalculator()
    
    # Generar dataset grande
    start_time = time.time()
    large_data = generator.generate_seismic_sequence(days=30, eruption_scenario=False)
    generation_time = time.time() - start_time
    
    print(f"Datos generados: {len(large_data)} eventos en {generation_time:.3f}s")
    
    # Calcular derivadas
    start_time = time.time()
    result = calculator.calculate_derivatives(large_data, show_steps=False)
    calculation_time = time.time() - start_time
    
    print(f"Derivadas calculadas en {calculation_time:.3f}s")
    print(f"Max aceleración: {result.max_acceleration:.6f}")
    print(f"Tendencia: {result.acceleration_trend}")
    
    # Verificar rendimiento
    if calculation_time < 5.0:  # Menos de 5 segundos
        print("✅ ÉXITO: Rendimiento aceptable")
        return True
    else:
        print(f"❌ FALLO: Rendimiento lento: {calculation_time:.3f}s")
        return False

def main():
    """Ejecuta todas las pruebas"""
    print("🚀 INICIANDO PRUEBAS DE VALIDACIÓN DEL SISTEMA DE DERIVADAS")
    print("=" * 70)
    
    tests = [
        test_normal_classification,
        test_eruption_classification,
        test_outlier_handling,
        test_edge_cases,
        test_performance
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ ERROR en prueba: {str(e)}")
            results.append(False)
    
    # Resumen
    print("\n" + "=" * 70)
    print("📊 RESUMEN DE PRUEBAS")
    print("=" * 70)
    
    passed = sum(results)
    total = len(results)
    
    print(f"Pruebas pasadas: {passed}/{total}")
    print(f"Porcentaje de éxito: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("🎉 ¡TODAS LAS PRUEBAS PASARON!")
    else:
        print("⚠️  Algunas pruebas fallaron. Revisar implementación.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
