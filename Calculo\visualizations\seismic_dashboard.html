
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌋 Visualizador de Datos Sísmicos</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: #ffffff;
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 3em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }
        
        .header .subtitle {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.15);
            padding: 20px;
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(5px);
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-card h3 {
            margin-bottom: 10px;
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .stat-value {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 0.9em;
            opacity: 0.8;
        }
        
        .chart-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }
        
        .chart-title {
            color: #2c3e50;
            font-size: 1.8em;
            margin-bottom: 20px;
            text-align: center;
            font-weight: bold;
        }
        
        .chart {
            width: 100%;
            height: 400px;
            position: relative;
        }
        
        .alert-panel {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            border-left: 5px solid #ff6b6b;
        }
        
        .alert-level {
            display: inline-block;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: bold;
            font-size: 1.2em;
            margin-bottom: 10px;
        }
        
        .alert-red {
            background: #ff6b6b;
            color: white;
        }
        
        .alert-orange {
            background: #ffa500;
            color: white;
        }
        
        .alert-yellow {
            background: #ffeb3b;
            color: #333;
        }
        
        .alert-green {
            background: #4caf50;
            color: white;
        }
        
        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .control-btn {
            padding: 10px 20px;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 25px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .control-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        .control-btn.active {
            background: #4caf50;
        }
        
        .tooltip {
            position: absolute;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 0.9em;
            pointer-events: none;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.3);
            opacity: 0.8;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .controls {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌋 Visualizador de Datos Sísmicos</h1>
            <p class="subtitle">Sistema de Predicción Volcánica - Análisis en Tiempo Real</p>
        </div>
        
        <div class="stats-grid">
            <div class="stat-card">
                <h3>📊 Eventos Totales</h3>
                <div class="stat-value" id="total-events">--</div>
                <div class="stat-label">Últimas 24 horas</div>
            </div>
            
            <div class="stat-card">
                <h3>📈 Magnitud Máxima</h3>
                <div class="stat-value" id="max-magnitude">--</div>
                <div class="stat-label">Escala de Richter</div>
            </div>
            
            <div class="stat-card">
                <h3>🎯 Probabilidad de Erupción</h3>
                <div class="stat-value" id="eruption-probability">--</div>
                <div class="stat-label">Análisis FFM</div>
            </div>
            
            <div class="stat-card">
                <h3>⚡ Aceleración Máxima</h3>
                <div class="stat-value" id="max-acceleration">--</div>
                <div class="stat-label">Derivada Segunda</div>
            </div>
        </div>
        
        <div class="alert-panel">
            <div class="alert-level" id="alert-level">VERDE</div>
            <div>
                <strong>Estado del Sistema:</strong> <span id="alert-message">Monitoreo normal</span>
            </div>
        </div>
        
        <div class="controls">
            <button class="control-btn active" onclick="showChart('magnitude', event)">📊 Magnitud vs Tiempo</button>
            <button class="control-btn" onclick="showChart('derivatives', event)">📈 Derivadas</button>
            <button class="control-btn" onclick="showChart('frequency', event)">📋 Distribución de Frecuencia</button>
            <button class="control-btn" onclick="showChart('patterns', event)">🔍 Patrones</button>
            <button class="control-btn" onclick="toggleRealTime(event)">⏱️ Tiempo Real</button>
        </div>
        
        <div class="chart-container">
            <div class="chart-title" id="chart-title">Magnitud Sísmica vs Tiempo</div>
            <div class="chart" id="main-chart"></div>
        </div>
        
        <div class="chart-container">
            <div class="chart-title">Análisis de Aceleración (Segunda Derivada)</div>
            <div class="chart" id="acceleration-chart"></div>
        </div>
        
        <div class="chart-container">
            <div class="chart-title">Patrón de Actividad en Tiempo Real</div>
            <div class="chart" id="realtime-chart"></div>
        </div>
        
        <div class="tooltip" id="tooltip"></div>
        
        <div class="footer">
            <p>Sistema desarrollado para análisis y predicción de actividad volcánica</p>
            <p>Datos actualizados: <span id="last-update">--</span></p>
        </div>
    </div>

    <script src="https://d3js.org/d3.v7.min.js"></script>
    <script>
        // Datos sísmicos (serán reemplazados por datos reales)
        let seismicData = [
  {
    "timestamp": "2025-07-08T04:33:17.416210",
    "magnitude": 1.749080237694725,
    "depth": 9.782560294561476,
    "frequency": 4.7782143788446225,
    "duration": 7.453942447208348
  },
  {
    "timestamp": "2025-07-08T05:33:17.416210",
    "magnitude": 1.312037280884873,
    "depth": 13.260289895074157,
    "frequency": 1.201975341512912,
    "duration": 1.0517943155978948
  },
  {
    "timestamp": "2025-07-08T06:33:17.416210",
    "magnitude": 2.2022300234864174,
    "depth": 14.608828078105926,
    "frequency": 3.6863266000822046,
    "duration": 0.6955526958101232
  },
  {
    "timestamp": "2025-07-08T10:33:17.416210",
    "magnitude": 2.6648852816008435,
    "depth": 4.384258628094639,
    "frequency": 1.4555259980522428,
    "duration": 2.2273371884674558
  },
  {
    "timestamp": "2025-07-08T12:33:17.416210",
    "magnitude": 1.6084844859190754,
    "depth": 5.785978822574545,
    "frequency": 2.86140394234507,
    "duration": 4.603477677100099
  },
  {
    "timestamp": "2025-07-08T13:33:17.416210",
    "magnitude": 2.223705789444759,
    "depth": 6.762703962817993,
    "frequency": 1.1277223729341883,
    "duration": 3.2753741610845726
  },
  {
    "timestamp": "2025-07-08T15:33:17.416210",
    "magnitude": 1.9121399684340719,
    "depth": 8.685047699376952,
    "frequency": 4.033291826268561,
    "duration": 2.3969009305044175
  },
  {
    "timestamp": "2025-07-08T16:33:17.416210",
    "magnitude": 2.184829137724085,
    "depth": 4.21681360793479,
    "frequency": 0.7090268572399898,
    "duration": 6.271676093063665
  },
  {
    "timestamp": "2025-07-08T19:33:17.416210",
    "magnitude": 1.130103185970559,
    "depth": 12.509165525513994,
    "frequency": 4.7699849176399995,
    "duration": 9.673504314208314
  },
  {
    "timestamp": "2025-07-08T22:33:17.416210",
    "magnitude": 1.6092275383467414,
    "depth": 7.721982418614817,
    "frequency": 0.9395245130287274,
    "duration": 7.00021375186549
  },
  {
    "timestamp": "2025-07-09T02:33:17.416210",
    "magnitude": 1.2440764696895577,
    "depth": 13.821165227024167,
    "frequency": 2.728296095500716,
    "duration": 0.8266909505945748
  },
  {
    "timestamp": "2025-07-09T05:33:17.416210",
    "magnitude": 1.5175599632000338,
    "depth": 8.76088427531154,
    "frequency": 3.481350279592919,
    "duration": 3.4612552228494042
  },
  {
    "timestamp": "2025-07-09T06:33:17.416210",
    "magnitude": 2.0934205586865593,
    "depth": 12.07672670369449,
    "frequency": 1.3318450498648717,
    "duration": 9.711053963763307
  },
  {
    "timestamp": "2025-07-09T13:33:17.416210",
    "magnitude": 4.483010989442761,
    "depth": 11.387268687887564,
    "frequency": 7.889431648353988,
    "duration": 8.475496866795853
  },
  {
    "timestamp": "2025-07-09T14:33:17.416210",
    "magnitude": 1.8831760065661425,
    "depth": 4.983435439937949,
    "frequency": 2.4874611855950763,
    "duration": 1.3015229425101562
  },
  {
    "timestamp": "2025-07-09T16:33:17.416210",
    "magnitude": 2.9961120052388246,
    "depth": 5.1205833619842815,
    "frequency": 3.294620945137989,
    "duration": 12.200666376688849
  },
  {
    "timestamp": "2025-07-09T18:33:17.416210",
    "magnitude": 2.7667394057495773,
    "depth": 9.232645128424727,
    "frequency": 5.968325673401725,
    "duration": 2.784438493565514
  },
  {
    "timestamp": "2025-07-09T19:33:17.416210",
    "magnitude": 2.08479804992374,
    "depth": 3.3392642408165183,
    "frequency": 10.305495962093426,
    "duration": 12.090330475691008
  },
  {
    "timestamp": "2025-07-09T20:33:17.416210",
    "magnitude": 1.8776535778876662,
    "depth": 8.197923703237738,
    "frequency": 8.93480663152876,
    "duration": 11.338084633153715
  },
  {
    "timestamp": "2025-07-09T21:33:17.416210",
    "magnitude": 4.69944621340081,
    "depth": 1.811083416675908,
    "frequency": 2.036625124277265,
    "duration": 11.395506127783905
  },
  {
    "timestamp": "2025-07-09T21:45:17.416210",
    "magnitude": 5.020861990564578,
    "depth": 1.4449084520021653,
    "frequency": 9.72617377558581,
    "duration": 10.596042720726826
  },
  {
    "timestamp": "2025-07-09T22:45:17.416210",
    "magnitude": 3.0884381260048177,
    "depth": 5.462902299486492,
    "frequency": 5.552566508374459,
    "duration": 22.158579171803858
  },
  {
    "timestamp": "2025-07-09T23:45:17.416210",
    "magnitude": 5.1052445990171424,
    "depth": 5.992713510560965,
    "frequency": 7.61100895226729,
    "duration": 4.46823313221075
  },
  {
    "timestamp": "2025-07-10T00:06:17.416210",
    "magnitude": 4.662747670159141,
    "depth": 4.456569174550735,
    "frequency": 8.857880765972947,
    "duration": 23.358048218682267
  },
  {
    "timestamp": "2025-07-10T01:06:17.416210",
    "magnitude": 3.829564902836979,
    "depth": 1.7552399889531312,
    "frequency": 6.985574257019695,
    "duration": 1.7371546755787604
  },
  {
    "timestamp": "2025-07-10T01:24:17.416210",
    "magnitude": 2.1100021499035697,
    "depth": 4.559994838152919,
    "frequency": 9.909745757692926,
    "duration": 10.116323451213473
  },
  {
    "timestamp": "2025-07-10T02:24:17.416210",
    "magnitude": 5.1764826587413255,
    "depth": 6.288857969801341,
    "frequency": 4.490091208084249,
    "duration": 12.901104768033262
  },
  {
    "timestamp": "2025-07-10T02:36:17.416210",
    "magnitude": 2.8007935792206786,
    "depth": 2.128549010778031,
    "frequency": 2.077718737603102,
    "duration": 9.402792134499272
  }
] || [];
        let derivativeData = [
  {
    "index": 0,
    "first_derivative": 0.4642198987659565,
    "second_derivative": -0.11901808301649652
  },
  {
    "index": 1,
    "first_derivative": 0.34520181574945996,
    "second_derivative": -0.19424555407872462
  },
  {
    "index": 2,
    "first_derivative": 0.07572879060850726,
    "second_derivative": -0.07039661823641222
  },
  {
    "index": 3,
    "first_derivative": -0.006781275432601171,
    "second_derivative": -0.036308839822477346
  },
  {
    "index": 4,
    "first_derivative": -0.1421242483263568,
    "second_derivative": 0.01346301700555403
  },
  {
    "index": 5,
    "first_derivative": 0.03360777558406092,
    "second_derivative": 0.03673622434994952
  },
  {
    "index": 6,
    "first_derivative": -0.03191557527650827,
    "second_derivative": -0.06339604570363437
  },
  {
    "index": 7,
    "first_derivative": -0.15658036152684218,
    "second_derivative": -0.012747721054381102
  },
  {
    "index": 8,
    "first_derivative": -0.08290645949403268,
    "second_derivative": 0.021830328151979744
  },
  {
    "index": 9,
    "first_derivative": -0.025598392614963723,
    "second_derivative": 0.01349504022772002
  },
  {
    "index": 10,
    "first_derivative": 0.011558822100007455,
    "second_derivative": 0.05387432622183718
  },
  {
    "index": 11,
    "first_derivative": 0.35152189093789654,
    "second_derivative": 0.05319740301669213
  },
  {
    "index": 12,
    "first_derivative": 0.22434843416677597,
    "second_derivative": -0.03993588607532766
  },
  {
    "index": 13,
    "first_derivative": 0.03203480233527528,
    "second_derivative": -0.05585292067500608
  },
  {
    "index": 14,
    "first_derivative": -0.22247493123327264,
    "second_derivative": -0.03558299684547955
  },
  {
    "index": 15,
    "first_derivative": -0.07471418820116338,
    "second_derivative": 0.010519080612324025
  },
  {
    "index": 16,
    "first_derivative": -0.18039860878397654,
    "second_derivative": -0.007260813510515551
  },
  {
    "index": 17,
    "first_derivative": -0.09649662873271003,
    "second_derivative": 0.6549525609992983
  },
  {
    "index": 18,
    "first_derivative": 1.12950651321462,
    "second_derivative": 0.8564903808747868
  },
  {
    "index": 19,
    "first_derivative": 1.6164841330168636,
    "second_derivative": -0.9655196807556797
  },
  {
    "index": 20,
    "first_derivative": -0.029117103692198446,
    "second_derivative": -1.4019327813794862
  },
  {
    "index": 21,
    "first_derivative": -0.06583520463852377,
    "second_derivative": 0.2786745097386613
  },
  {
    "index": 22,
    "first_derivative": 0.5282319157851242,
    "second_derivative": -0.5425980673670945
  },
  {
    "index": 23,
    "first_derivative": -0.7983425955840983,
    "second_derivative": -1.1944920181732537
  },
  {
    "index": 24,
    "first_derivative": -1.0843323087487615,
    "second_derivative": 0.6471557451405395
  },
  {
    "index": 25,
    "first_derivative": 0.042959873098605945,
    "second_derivative": 0.8409034864355638
  },
  {
    "index": 26,
    "first_derivative": 0.008842223617474955,
    "second_derivative": 0.49256478306099566
  },
  {
    "index": 27,
    "first_derivative": 0.6340376127717986,
    "second_derivative": 3.1259769457716846
  }
] || [];
        let ffmData = {
  "failure_probability": 0.8217567264771249,
  "time_to_failure": 0.9822920035555567,
  "confidence_level": 0.516740216563397,
  "acceleration_trend": "ACELERACI\u00d3N EXPONENCIAL",
  "precursor_indicators": [
    "ACELERACI\u00d3N S\u00cdSMICA CR\u00cdTICA",
    "PATR\u00d3N CR\u00cdTICO DETECTADO"
  ]
} || {};
        let alertData = {
  "level": "RED",
  "message": "Indicadores cr\u00edticos sugieren erupci\u00f3n inmediata. Probabilidad de erupci\u00f3n: 82.2% | Tiempo estimado: 1.0 horas | Precursores detectados: 2 | Tendencia: ACELERACI\u00d3N EXPONENCIAL",
  "probability": 0.8217567264771249,
  "timestamp": "2025-07-10T03:33:17.418918"
} || {};
        
        // Variables globales
        let currentChart = 'magnitude';
        let isRealTimeActive = false;
        let realTimeInterval = null;
        
        // Funciones principales
        function updateStats() {
            if (!seismicData || seismicData.length === 0) {
                document.getElementById('total-events').textContent = '0';
                document.getElementById('max-magnitude').textContent = '0.0';
                document.getElementById('eruption-probability').textContent = '0.0%';
                document.getElementById('max-acceleration').textContent = '0.0';
                return;
            }
            
            const totalEvents = seismicData.length;
            const maxMagnitude = Math.max(...seismicData.map(d => d.magnitude || 0));
            const maxAcceleration = derivativeData && derivativeData.length > 0 ? 
                Math.max(...derivativeData.map(d => Math.abs(d.second_derivative || 0))) : 0;
            const eruptionProbability = ffmData.failure_probability || 0;
            
            document.getElementById('total-events').textContent = totalEvents;
            document.getElementById('max-magnitude').textContent = maxMagnitude.toFixed(2);
            document.getElementById('eruption-probability').textContent = (eruptionProbability * 100).toFixed(1) + '%';
            document.getElementById('max-acceleration').textContent = maxAcceleration.toFixed(2);
        }
        
        function updateAlertStatus() {
            const alertLevel = alertData.level || 'GREEN';
            const alertMessage = alertData.message || 'Sistema funcionando normalmente';
            
            const levelElement = document.getElementById('alert-level');
            levelElement.textContent = alertLevel;
            levelElement.className = `alert-level alert-${alertLevel.toLowerCase()}`;
            
            document.getElementById('alert-message').textContent = alertMessage;
        }
        
        function updateLastUpdate() {
            document.getElementById('last-update').textContent = new Date().toLocaleString();
        }
        
        // Inicializar visualización
        document.addEventListener('DOMContentLoaded', function() {
            updateStats();
            updateAlertStatus();
            showChart('magnitude');
            updateLastUpdate();
        });
        
        function showChart(chartType, event) {
            // Actualizar botones
            document.querySelectorAll('.control-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // Solo agregar clase active si hay un evento (llamada desde botón)
            if (event && event.target) {
                event.target.classList.add('active');
            } else {
                // Si no hay evento, encontrar el botón correspondiente
                const buttons = document.querySelectorAll('.control-btn');
                buttons.forEach(btn => {
                    if (btn.textContent.includes('Magnitud') && chartType === 'magnitude') {
                        btn.classList.add('active');
                    }
                });
            }
            
            currentChart = chartType;
            
            try {
                switch(chartType) {
                    case 'magnitude':
                        drawMagnitudeChart();
                        break;
                    case 'derivatives':
                        drawDerivativeChart();
                        break;
                    case 'frequency':
                        drawFrequencyChart();
                        break;
                    case 'patterns':
                        drawPatternChart();
                        break;
                    default:
                        console.warn('Tipo de gráfico no reconocido:', chartType);
                        drawMagnitudeChart();
                }
            } catch (error) {
                console.error('Error al dibujar gráfico:', error);
                document.getElementById('chart-title').textContent = 'Error al cargar gráfico';
            }
        }
        
        function drawMagnitudeChart() {
            const container = d3.select('#main-chart');
            container.selectAll('*').remove();
            
            // Validar datos
            if (!seismicData || seismicData.length === 0) {
                container.append('div')
                    .style('text-align', 'center')
                    .style('padding', '50px')
                    .style('color', '#666')
                    .html('📊 No hay datos sísmicos disponibles');
                document.getElementById('chart-title').textContent = 'Datos No Disponibles';
                return;
            }
            
            const margin = {top: 20, right: 30, bottom: 40, left: 50};
            const width = container.node().getBoundingClientRect().width - margin.left - margin.right;
            const height = 400 - margin.top - margin.bottom;
            
            if (width <= 0 || height <= 0) {
                console.warn('Dimensiones inválidas para el gráfico');
                return;
            }
            
            const svg = container.append('svg')
                .attr('width', width + margin.left + margin.right)
                .attr('height', height + margin.top + margin.bottom);
            
            const g = svg.append('g')
                .attr('transform', `translate(${margin.left},${margin.top})`);
            
            // Escalas
            const xScale = d3.scaleTime()
                .domain(d3.extent(seismicData, d => new Date(d.timestamp)))
                .range([0, width]);
            
            const yScale = d3.scaleLinear()
                .domain(d3.extent(seismicData, d => d.magnitude))
                .range([height, 0]);
            
            // Línea
            const line = d3.line()
                .x(d => xScale(new Date(d.timestamp)))
                .y(d => yScale(d.magnitude))
                .curve(d3.curveMonotoneX);
            
            // Dibujar ejes
            g.append('g')
                .attr('transform', `translate(0,${height})`)
                .call(d3.axisBottom(xScale).tickFormat(d3.timeFormat('%H:%M')));
            
            g.append('g')
                .call(d3.axisLeft(yScale));
            
            // Dibujar línea
            g.append('path')
                .datum(seismicData)
                .attr('fill', 'none')
                .attr('stroke', '#ff6b6b')
                .attr('stroke-width', 2)
                .attr('d', line);
            
            // Puntos
            g.selectAll('.dot')
                .data(seismicData)
                .enter().append('circle')
                .attr('class', 'dot')
                .attr('cx', d => xScale(new Date(d.timestamp)))
                .attr('cy', d => yScale(d.magnitude))
                .attr('r', 4)
                .attr('fill', d => d.magnitude > 4 ? '#ff4444' : '#4CAF50')
                .on('mouseover', function(event, d) {
                    showTooltip(event, `Magnitud: ${d.magnitude.toFixed(2)}<br>Tiempo: ${new Date(d.timestamp).toLocaleString()}`);
                })
                .on('mouseout', hideTooltip);
            
            // Etiquetas de ejes
            g.append('text')
                .attr('transform', 'rotate(-90)')
                .attr('y', 0 - margin.left)
                .attr('x', 0 - (height / 2))
                .attr('dy', '1em')
                .style('text-anchor', 'middle')
                .style('fill', '#2c3e50')
                .text('Magnitud (Richter)');
            
            g.append('text')
                .attr('transform', `translate(${width / 2}, ${height + margin.bottom})`)
                .style('text-anchor', 'middle')
                .style('fill', '#2c3e50')
                .text('Tiempo');
            
            document.getElementById('chart-title').textContent = 'Magnitud Sísmica vs Tiempo';
        }
        
        function drawDerivativeChart() {
            const container = d3.select('#main-chart');
            container.selectAll('*').remove();
            
            // Validar datos
            if (!derivativeData || derivativeData.length === 0) {
                container.append('div')
                    .style('text-align', 'center')
                    .style('padding', '50px')
                    .style('color', '#666')
                    .html('📈 No hay datos de derivadas disponibles');
                document.getElementById('chart-title').textContent = 'Datos de Derivadas No Disponibles';
                return;
            }
            
            const margin = {top: 20, right: 30, bottom: 40, left: 50};
            const width = container.node().getBoundingClientRect().width - margin.left - margin.right;
            const height = 400 - margin.top - margin.bottom;
            
            if (width <= 0 || height <= 0) {
                console.warn('Dimensiones inválidas para el gráfico de derivadas');
                return;
            }
            
            const svg = container.append('svg')
                .attr('width', width + margin.left + margin.right)
                .attr('height', height + margin.top + margin.bottom);
            
            const g = svg.append('g')
                .attr('transform', `translate(${margin.left},${margin.top})`);
            
            // Escalas
            const xScale = d3.scaleLinear()
                .domain([0, derivativeData.length - 1])
                .range([0, width]);
            
            const yScale = d3.scaleLinear()
                .domain(d3.extent(derivativeData, d => d.first_derivative || 0))
                .range([height, 0]);
            
            // Línea para primera derivada
            const line = d3.line()
                .x((d, i) => xScale(i))
                .y(d => yScale(d.first_derivative || 0))
                .curve(d3.curveMonotoneX);
            
            // Dibujar ejes
            g.append('g')
                .attr('transform', `translate(0,${height})`)
                .call(d3.axisBottom(xScale));
            
            g.append('g')
                .call(d3.axisLeft(yScale));
            
            // Línea de referencia en y=0
            g.append('line')
                .attr('x1', 0)
                .attr('x2', width)
                .attr('y1', yScale(0))
                .attr('y2', yScale(0))
                .attr('stroke', '#666')
                .attr('stroke-dasharray', '3,3');
            
            // Dibujar línea
            g.append('path')
                .datum(derivativeData)
                .attr('fill', 'none')
                .attr('stroke', '#2196F3')
                .attr('stroke-width', 2)
                .attr('d', line);
            
            // Puntos críticos
            g.selectAll('.critical-point')
                .data(derivativeData.filter(d => Math.abs(d.first_derivative || 0) > 0.5))
                .enter().append('circle')
                .attr('class', 'critical-point')
                .attr('cx', (d, i) => xScale(derivativeData.indexOf(d)))
                .attr('cy', d => yScale(d.first_derivative || 0))
                .attr('r', 6)
                .attr('fill', '#ff6b6b')
                .attr('stroke', '#fff')
                .attr('stroke-width', 2);
            
            document.getElementById('chart-title').textContent = 'Análisis de Derivadas - Velocidad de Cambio';
        }
        
        function drawFrequencyChart() {
            const container = d3.select('#main-chart');
            container.selectAll('*').remove();
            
            // Validar datos
            if (!seismicData || seismicData.length === 0) {
                container.append('div')
                    .style('text-align', 'center')
                    .style('padding', '50px')
                    .style('color', '#666')
                    .html('📋 No hay datos para histograma');
                document.getElementById('chart-title').textContent = 'Datos No Disponibles';
                return;
            }
            
            // Crear histograma de magnitudes
            const magnitudes = seismicData.map(d => d.magnitude || 0);
            const bins = d3.histogram()
                .domain(d3.extent(magnitudes))
                .thresholds(10)(magnitudes);
            
            const margin = {top: 20, right: 30, bottom: 40, left: 50};
            const width = container.node().getBoundingClientRect().width - margin.left - margin.right;
            const height = 400 - margin.top - margin.bottom;
            
            const svg = container.append('svg')
                .attr('width', width + margin.left + margin.right)
                .attr('height', height + margin.top + margin.bottom);
            
            const g = svg.append('g')
                .attr('transform', `translate(${margin.left},${margin.top})`);
            
            const xScale = d3.scaleLinear()
                .domain(d3.extent(magnitudes))
                .range([0, width]);
            
            const yScale = d3.scaleLinear()
                .domain([0, d3.max(bins, d => d.length)])
                .range([height, 0]);
            
            // Dibujar ejes
            g.append('g')
                .attr('transform', `translate(0,${height})`)
                .call(d3.axisBottom(xScale));
            
            g.append('g')
                .call(d3.axisLeft(yScale));
            
            // Barras
            g.selectAll('.bar')
                .data(bins)
                .enter().append('rect')
                .attr('class', 'bar')
                .attr('x', d => xScale(d.x0))
                .attr('y', d => yScale(d.length))
                .attr('width', d => xScale(d.x1) - xScale(d.x0) - 1)
                .attr('height', d => height - yScale(d.length))
                .attr('fill', '#4CAF50')
                .attr('opacity', 0.7);
            
            document.getElementById('chart-title').textContent = 'Distribución de Frecuencia de Magnitudes';
        }
        
        function drawPatternChart() {
            // Implementar gráfico de patrones
            document.getElementById('chart-title').textContent = 'Análisis de Patrones Sísmicos';
        }
        
        function toggleRealTime(event) {
            isRealTimeActive = !isRealTimeActive;
            const btn = event ? event.target : document.querySelector('.control-btn:last-child');
            
            if (isRealTimeActive) {
                btn.textContent = '⏹️ Detener Tiempo Real';
                btn.classList.add('active');
                startRealTimeSimulation();
            } else {
                btn.textContent = '⏱️ Tiempo Real';
                btn.classList.remove('active');
                stopRealTimeSimulation();
            }
        }
        
        function startRealTimeSimulation() {
            realTimeInterval = setInterval(() => {
                // Simular nuevos datos
                const newMagnitude = Math.random() * 3 + 1;
                const newTimestamp = new Date().toISOString();
                
                seismicData.push({
                    timestamp: newTimestamp,
                    magnitude: newMagnitude,
                    depth: Math.random() * 10 + 5
                });
                
                // Mantener solo los últimos 100 puntos
                if (seismicData.length > 100) {
                    seismicData.shift();
                }
                
                updateStats();
                if (currentChart === 'magnitude') {
                    drawMagnitudeChart();
                }
                
                updateLastUpdate();
            }, 2000);
        }
        
        function stopRealTimeSimulation() {
            if (realTimeInterval) {
                clearInterval(realTimeInterval);
                realTimeInterval = null;
            }
        }
        
        function showTooltip(event, html) {
            const tooltip = document.getElementById('tooltip');
            tooltip.innerHTML = html;
            tooltip.style.left = (event.pageX + 10) + 'px';
            tooltip.style.top = (event.pageY - 10) + 'px';
            tooltip.style.opacity = '1';
        }
        
        function hideTooltip() {
            document.getElementById('tooltip').style.opacity = '0';
        }
        
        function updateLastUpdate() {
            document.getElementById('last-update').textContent = new Date().toLocaleString();
        }
        
        // Redimensionar gráficos cuando cambie el tamaño de la ventana
        window.addEventListener('resize', function() {
            if (currentChart) {
                showChart(currentChart);
            }
        });
    </script>
</body>
</html>
        