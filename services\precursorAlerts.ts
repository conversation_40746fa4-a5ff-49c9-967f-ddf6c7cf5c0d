/**
 * 🌋 Volcano App - Servicio de Alertas de Precursores
 * Integración del análisis de precursores con el sistema de alertas de Supabase
 */

import { supabase } from './supabase';
import { ResultadoAlerta, NivelAlerta } from '@/types/precursor';
import { AlertLevel } from '@/packages/shared-types/src/alerts';

/**
 * Mapeo de niveles de alerta de precursores a niveles de alerta volcánica
 */
const MAPEO_NIVELES_ALERTA: Record<NivelAlerta, AlertLevel> = {
  Verde: AlertLevel.NORMAL,
  Amarillo: AlertLevel.ADVISORY,
  Rojo: AlertLevel.WARNING,
};

/**
 * Configuración de alertas por nivel
 */
const CONFIGURACION_ALERTAS = {
  Verde: {
    prioridad: 'low' as const,
    enviarNotificacion: false,
    duracionHoras: 1,
  },
  Amarillo: {
    prioridad: 'medium' as const,
    enviarNotificacion: true,
    duracionHoras: 6,
  },
  Rojo: {
    prioridad: 'high' as const,
    enviarNotificacion: true,
    duracionHoras: 24,
  },
};

/**
 * Interface para datos de alerta de precursores
 */
export interface AlertaPrecursor {
  titulo: string;
  mensaje: string;
  nivel: NivelAlerta;
  valor: number | null;
  volcanoName?: string;
  volcanoLat?: number;
  volcanoLng?: number;
  metadata?: {
    tipoPrecursor?: string;
    valorSegundaDerivada?: number;
    timestamp?: string;
    datosOriginales?: number[];
  };
}

/**
 * Resultado de creación de alerta
 */
export interface ResultadoCreacionAlerta {
  success: boolean;
  alertaId?: string;
  error?: string;
  notificacionEnviada?: boolean;
}

/**
 * Servicio para manejo de alertas de precursores
 */
export class PrecursorAlertsService {
  private static instance: PrecursorAlertsService;
  private ultimaAlertaEnviada: Date | null = null;
  private nivelUltimaAlerta: NivelAlerta | null = null;

  private constructor() {}

  public static getInstance(): PrecursorAlertsService {
    if (!PrecursorAlertsService.instance) {
      PrecursorAlertsService.instance = new PrecursorAlertsService();
    }
    return PrecursorAlertsService.instance;
  }

  /**
   * Crear alerta de precursor en Supabase
   */
  async crearAlertaPrecursor(alertaPrecursor: AlertaPrecursor): Promise<ResultadoCreacionAlerta> {
    try {
      const configuracion = CONFIGURACION_ALERTAS[alertaPrecursor.nivel];
      const alertLevel = MAPEO_NIVELES_ALERTA[alertaPrecursor.nivel];

      // Verificar si debemos enviar la alerta (evitar spam)
      if (!this.debeEnviarAlerta(alertaPrecursor.nivel)) {
        console.log(`🔇 Alerta de precursor ${alertaPrecursor.nivel} omitida para evitar spam`);
        return {
          success: true,
          notificacionEnviada: false,
        };
      }

      // Calcular fecha de expiración
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + configuracion.duracionHoras);

      // Preparar datos de la alerta
      const alertData = {
        title: alertaPrecursor.titulo,
        message: alertaPrecursor.mensaje,
        alert_level: alertLevel,
        volcano_name: alertaPrecursor.volcanoName || 'Volcán Villarrica',
        volcano_lat: alertaPrecursor.volcanoLat || -39.420000,
        volcano_lng: alertaPrecursor.volcanoLng || -71.939167,
        is_active: true,
        is_scheduled: false,
        expires_at: expiresAt.toISOString(),
        metadata: {
          ...alertaPrecursor.metadata,
          source: 'precursor_analysis',
          nivel_precursor: alertaPrecursor.nivel,
          valor_segunda_derivada: alertaPrecursor.valor,
          prioridad: configuracion.prioridad,
          created_by_system: true,
        },
      };

      // Insertar alerta en Supabase
      const { data, error } = await supabase
        .from('volcano_alerts')
        .insert([alertData])
        .select()
        .single();

      if (error) {
        console.error('❌ Error creando alerta de precursor:', error);
        return {
          success: false,
          error: error.message,
        };
      }

      // Actualizar estado interno
      this.ultimaAlertaEnviada = new Date();
      this.nivelUltimaAlerta = alertaPrecursor.nivel;

      console.log(`✅ Alerta de precursor creada: ${data.id} (${alertaPrecursor.nivel})`);

      return {
        success: true,
        alertaId: data.id,
        notificacionEnviada: configuracion.enviarNotificacion,
      };

    } catch (error) {
      console.error('❌ Error en crearAlertaPrecursor:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Error desconocido',
      };
    }
  }

  /**
   * Procesar resultado de análisis de precursores y crear alerta si es necesario
   */
  async procesarResultadoAnalisis(
    resultado: ResultadoAlerta,
    tipoPrecursor: string = 'Actividad Sísmica',
    volcanoName?: string
  ): Promise<ResultadoCreacionAlerta | null> {
    // Solo crear alertas para niveles Amarillo y Rojo
    if (resultado.nivel === 'Verde') {
      return null;
    }

    const alertaPrecursor: AlertaPrecursor = {
      titulo: `Análisis de Precursores: ${resultado.nivel}`,
      mensaje: `${resultado.mensaje}. ${tipoPrecursor} muestra aceleración significativa.`,
      nivel: resultado.nivel,
      valor: resultado.valor,
      volcanoName,
      metadata: {
        tipoPrecursor,
        valorSegundaDerivada: resultado.valor,
        timestamp: resultado.timestamp.toISOString(),
        indice: resultado.indice,
      },
    };

    return await this.crearAlertaPrecursor(alertaPrecursor);
  }

  /**
   * Determinar si se debe enviar una alerta para evitar spam
   */
  private debeEnviarAlerta(nivel: NivelAlerta): boolean {
    const ahora = new Date();
    
    // Siempre enviar alertas Rojas
    if (nivel === 'Rojo') {
      return true;
    }

    // Para alertas Amarillas, esperar al menos 30 minutos entre envíos
    if (nivel === 'Amarillo') {
      if (!this.ultimaAlertaEnviada) {
        return true;
      }
      
      const tiempoTranscurrido = ahora.getTime() - this.ultimaAlertaEnviada.getTime();
      const treintaMinutos = 30 * 60 * 1000;
      
      return tiempoTranscurrido >= treintaMinutos;
    }

    // No enviar alertas Verdes automáticamente
    return false;
  }

  /**
   * Obtener alertas de precursores activas
   */
  async obtenerAlertasActivas(): Promise<any[]> {
    try {
      const { data, error } = await supabase
        .from('volcano_alerts')
        .select('*')
        .eq('is_active', true)
        .contains('metadata', { source: 'precursor_analysis' })
        .order('created_at', { ascending: false });

      if (error) {
        console.error('❌ Error obteniendo alertas de precursores:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('❌ Error en obtenerAlertasActivas:', error);
      return [];
    }
  }

  /**
   * Desactivar alertas de precursores expiradas
   */
  async limpiarAlertasExpiradas(): Promise<void> {
    try {
      const ahora = new Date().toISOString();
      
      const { error } = await supabase
        .from('volcano_alerts')
        .update({ is_active: false })
        .contains('metadata', { source: 'precursor_analysis' })
        .lt('expires_at', ahora)
        .eq('is_active', true);

      if (error) {
        console.error('❌ Error limpiando alertas expiradas:', error);
      } else {
        console.log('🧹 Alertas de precursores expiradas limpiadas');
      }
    } catch (error) {
      console.error('❌ Error en limpiarAlertasExpiradas:', error);
    }
  }

  /**
   * Obtener estadísticas de alertas de precursores
   */
  async obtenerEstadisticas(): Promise<{
    totalAlertas: number;
    alertasActivas: number;
    alertasPorNivel: Record<string, number>;
    ultimaAlerta?: Date;
  }> {
    try {
      const { data, error } = await supabase
        .from('volcano_alerts')
        .select('alert_level, is_active, created_at, metadata')
        .contains('metadata', { source: 'precursor_analysis' })
        .order('created_at', { ascending: false });

      if (error) {
        console.error('❌ Error obteniendo estadísticas:', error);
        return {
          totalAlertas: 0,
          alertasActivas: 0,
          alertasPorNivel: {},
        };
      }

      const alertas = data || [];
      const alertasActivas = alertas.filter(a => a.is_active);
      const alertasPorNivel = alertas.reduce((acc, alerta) => {
        const nivel = alerta.metadata?.nivel_precursor || alerta.alert_level;
        acc[nivel] = (acc[nivel] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      return {
        totalAlertas: alertas.length,
        alertasActivas: alertasActivas.length,
        alertasPorNivel,
        ultimaAlerta: alertas.length > 0 ? new Date(alertas[0].created_at) : undefined,
      };
    } catch (error) {
      console.error('❌ Error en obtenerEstadisticas:', error);
      return {
        totalAlertas: 0,
        alertasActivas: 0,
        alertasPorNivel: {},
      };
    }
  }

  /**
   * Resetear estado interno (útil para testing)
   */
  resetearEstado(): void {
    this.ultimaAlertaEnviada = null;
    this.nivelUltimaAlerta = null;
  }
}

// Exportar instancia singleton
export const precursorAlertsService = PrecursorAlertsService.getInstance();
