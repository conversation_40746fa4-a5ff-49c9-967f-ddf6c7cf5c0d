# VolcanApp - <PERSON>ó<PERSON>lo de Cálculo
# Versiones compatibles que NO requieren compilación C++

# === DEPENDENCIAS BÁSICAS (API) ===
fastapi>=0.104.1
uvicorn>=0.24.0
pydantic>=2.8.0
pydantic-settings>=2.1.0
python-multipart>=0.0.6
httpx>=0.25.2

# === DEPENDENCIAS CIENTÍFICAS ===
# Estas versiones tienen wheels precompilados para Python 3.13
numpy>=1.26.0          # Antes: 1.24.3 (requería compilación)
scipy>=1.12.0          # Antes: 1.11.4 (requería compilación)
matplotlib>=3.8.0      # Antes: 3.7.2 (requería compilación)
pandas>=2.1.0          # Antes: 2.0.3 (requería compilación)
sympy>=1.12            # No requiere compilación (Python puro)

# === DEPENDENCIAS DE DESARROLLO ===
pytest>=7.4.3
pytest-asyncio>=0.21.1
pytest-cov>=4.0.0
black>=23.0.0
flake8>=6.0.0
mypy>=1.5.0

# NOTA: Las versiones con ">=" permiten que UV/pip elijan 
# automáticamente la versión más reciente compatible
